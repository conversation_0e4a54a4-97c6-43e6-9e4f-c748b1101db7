import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { MongooseModule } from '@nestjs/mongoose';
import { DocumentCacheEntity } from './modules/documents/entities/document-cache.entity';
import { DocumentsModule } from './modules/documents/documents.module';
import { AIModule } from './modules/ai/ai.module';
import { PromptTemplateModule } from './modules/prompt-templates/prompt-template.module';
import { ChatModule } from './modules/chat/chat.module';
import { ContextManagementModule } from './modules/context-management/context-management.module';
import { AnalyticsModule } from './modules/analytics/analytics.module';
import { appConfig } from './config/app.config';
import { openaiConfig } from './config/openai.config';
import { promptTemplatesConfig } from './config/prompt-templates.config';
import { systemPromptConfig } from './config/system-prompt.config';
import contextManagementConfig from './config/context-management.config';
import { DatabaseModule } from './modules/database/database.module';
import { databaseConfig } from './config/database.config';
import { QueueModule } from './modules/queue/queue.module';
import { queueConfig } from './config/queue.config';
import { MigrationModule } from './modules/migration/migration.module';
import { AuthModule } from './modules/auth/auth.module';
import { authConfig } from './config/auth.config';
import { SubscriptionModule } from './modules/subscription/subscription.module';
import { subscriptionConfig } from './config/subscription.config';
import { featureCostsConfig } from './config/feature-costs.config';
import { TenantContextMiddleware } from './modules/auth/middleware/tenant-context.middleware';
import { SharedModule } from './modules/shared/shared.module';
import { LegalResearchModule } from './modules/legal-research/legal-research.module';
import { ActivityLoggingModule } from './modules/activity-logging/activity-logging.module';
import { DocumentOrganizationModule } from './modules/document-organization/document-organization.module';
import { UserFeedbackModule } from './modules/user-feedback/user-feedback.module';
import { AuditModule } from './modules/audit/audit.module';
import { auditConfig } from './config/audit.config';
import { RequestContextMiddleware } from './modules/auth/middleware/request-context.middleware';
import { CreditUsageMiddleware } from './modules/subscription/middleware/credit-usage.middleware';
import { CollaborationModule } from './modules/collaboration/collaboration.module';
import { WorkflowModule } from './modules/workflow/workflow.module';
import { EmailAutomationModule } from './modules/email-automation/email-automation.module';
import { GamificationModule } from './modules/gamification/gamification.module';
import { ChatNegotiationModule } from './modules/chat-negotiation/chat-negotiation.module';
import { LegalResearchAssistantModule } from './modules/legal-research-assistant/legal-research-assistant.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      envFilePath: ['.env.development', '.env'],
      isGlobal: true,
      load: [
        appConfig,
        openaiConfig,
        promptTemplatesConfig,
        systemPromptConfig,
        databaseConfig,
        contextManagementConfig,
        queueConfig,
        authConfig,
        subscriptionConfig,
        featureCostsConfig,
        auditConfig,
      ],
    }),
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: '1d' },
    }),
    TypeOrmModule.forRoot({
      type: 'sqlite',
      database: 'uploads/cache/document-cache.db',
      entities: [DocumentCacheEntity],
      synchronize: true,
      logging: true,
    }),
    ScheduleModule.forRoot(),
    MongooseModule.forRoot(process.env.DATABASE_URI),
    DatabaseModule,
    QueueModule,
    AuthModule,
    AIModule,
    DocumentsModule,
    PromptTemplateModule,
    ContextManagementModule,
    ChatModule,
    MigrationModule,
    SubscriptionModule,
    SharedModule,
    AnalyticsModule,
    LegalResearchModule,
    ActivityLoggingModule,
    DocumentOrganizationModule,
    UserFeedbackModule,
    AuditModule,
    CollaborationModule,
    WorkflowModule,
    EmailAutomationModule,
    GamificationModule,
    ChatNegotiationModule,
    LegalResearchAssistantModule,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(TenantContextMiddleware, RequestContextMiddleware, CreditUsageMiddleware)
      .forRoutes('*');
  }
}
