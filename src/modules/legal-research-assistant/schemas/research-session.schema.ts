import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type ResearchSessionDocument = ResearchSession & Document;

export interface ResearchQueryMetadata {
  sourcesFound: number;
  synthesisGenerated: boolean;
  responseTime: number; // milliseconds
  sourceBreakdown?: {
    [sourceType: string]: number;
  };
}

export interface ResearchQueryRecord {
  queryId: string;
  query?: string;
  question?: string; // For follow-up queries
  timestamp: Date;
  creditsUsed: number;
  resultSummary: string;
  type: 'initial' | 'followup';
  metadata?: ResearchQueryMetadata;
  // Store complete research results for session history
  searchResults?: {
    totalSources: number;
    sources: Array<{
      id: string;
      type: string;
      title: string;
      citation?: string;
      court?: string;
      authority?: string;
      date: string;
      jurisdiction: string;
      practiceArea?: string;
      url: string;
      snippet: string;
      relevanceScore: number;
      authorityScore: number;
    }>;
    metadata: {
      duration: number;
      cacheHit: boolean;
      searchStrategies: string[];
      sourceBreakdown?: { [sourceType: string]: number };
    };
  };
  aiSynthesis?: {
    legalAnalysis: string;
    keyFindings: Array<{
      finding: string;
      sourceUrls: string[];
      confidence?: number;
    }>;
    citations: string[];
    confidenceScore: number;
    practiceImplications: string[];
    jurisdictionalNotes?: string;
    recentDevelopments?: string;
    metadata: {
      duration: number;
      sourcesAnalyzed: number;
      aiModel: string;
      promptVersion?: string;
    };
  };
  followUpSuggestions?: string[];
}

export interface SessionCollaborator {
  userId: string;
  email: string;
  role: 'viewer' | 'collaborator' | 'owner';
  sharedAt: Date;
}

export interface SessionMetadata {
  lastAccessedAt?: Date;
  sharedWith?: string[];
  collaborators?: SessionCollaborator[];
  totalSearchDuration?: number; // milliseconds
  totalSynthesisDuration?: number; // milliseconds
  averageResponseTime?: number; // milliseconds
  topPracticeAreas?: string[];
  topJurisdictions?: string[];
  [key: string]: any;
}

@Schema({
  collection: 'research_sessions',
  timestamps: true,
})
export class ResearchSession {
  @Prop({ required: true, unique: true, index: true })
  sessionId: string;

  @Prop({ required: true, index: true })
  organizationId: string;

  @Prop({ required: true, index: true })
  userId: string;

  @Prop({ required: true, trim: true })
  title: string;

  @Prop({ trim: true })
  description?: string;

  @Prop({ type: [String], default: [], index: true })
  tags: string[];

  @Prop({ default: false, index: true })
  isShared: boolean;

  @Prop({ default: 0 })
  queryCount: number;

  @Prop({ type: [Object], default: [] })
  queries: ResearchQueryRecord[];

  @Prop({ default: 0 })
  totalCreditsUsed: number;

  @Prop({ type: Object, default: {} })
  metadata: SessionMetadata;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

export const ResearchSessionSchema = SchemaFactory.createForClass(ResearchSession);

// Add indexes for better query performance
ResearchSessionSchema.index({ organizationId: 1, userId: 1 });
ResearchSessionSchema.index({ organizationId: 1, isShared: 1 });
ResearchSessionSchema.index({ tags: 1 });
ResearchSessionSchema.index({ createdAt: -1 });
ResearchSessionSchema.index({ updatedAt: -1 });
ResearchSessionSchema.index({ 'metadata.lastAccessedAt': -1 });

// Text index for search functionality
ResearchSessionSchema.index({ 
  title: 'text', 
  description: 'text',
  tags: 'text'
});

// Compound indexes for common query patterns
ResearchSessionSchema.index({ 
  organizationId: 1, 
  userId: 1, 
  updatedAt: -1 
});

ResearchSessionSchema.index({ 
  organizationId: 1, 
  isShared: 1, 
  updatedAt: -1 
});

// Pre-save middleware to update timestamps and metadata
ResearchSessionSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Update metadata
  if (!this.metadata) {
    this.metadata = {};
  }
  
  this.metadata.lastAccessedAt = new Date();
  
  // Calculate aggregated statistics
  if (this.queries && this.queries.length > 0) {
    const practiceAreas = new Set<string>();
    const jurisdictions = new Set<string>();
    let totalSearchDuration = 0;
    let totalSynthesisDuration = 0;
    let responseTimeSum = 0;
    let responseTimeCount = 0;
    
    this.queries.forEach(query => {
      if (query.metadata) {
        if (query.metadata.responseTime) {
          responseTimeSum += query.metadata.responseTime;
          responseTimeCount++;
        }
        
        // Extract practice areas and jurisdictions from metadata
        // This would be populated by the search service
        if (query.metadata.sourceBreakdown) {
          Object.keys(query.metadata.sourceBreakdown).forEach(area => {
            if (area.includes('_')) {
              practiceAreas.add(area);
            }
          });
        }
      }
    });
    
    this.metadata.averageResponseTime = responseTimeCount > 0 
      ? responseTimeSum / responseTimeCount 
      : 0;
    
    this.metadata.topPracticeAreas = Array.from(practiceAreas).slice(0, 5);
    this.metadata.topJurisdictions = Array.from(jurisdictions).slice(0, 5);
  }
  
  next();
});

// Model name constant for injection
export const RESEARCH_SESSION_MODEL = 'ResearchSession';
