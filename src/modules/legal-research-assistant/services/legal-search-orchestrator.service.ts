import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  SearchQuery,
  SearchSourceResult,
  SearchAggregationResult
} from '../interfaces/search-source.interface';
import { SearchResults } from '../interfaces/legal-research-result.interface';
import { LegalResearchQueryDto } from '../dto/legal-research-query.dto';
import { CourtListenerService } from '../../shared/court-listener/court-listener.service';
import { WebSearchService } from './web-search.service';
import { 
  SEARCH_SOURCE_CONFIGS, 
  TIER_LIMITATIONS, 
  getSourcesForTier,
  RELEVANCE_FACTORS,
  AUTHORITY_SCORES
} from '../constants/search-sources.constants';
import { classifyPracticeArea, extractJurisdictions } from '../constants/practice-areas.constants';

@Injectable()
export class LegalSearchOrchestratorService {
  private readonly logger = new Logger(LegalSearchOrchestratorService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly courtListenerService: CourtListenerService,
    private readonly webSearchService: WebSearchService,
  ) {}

  async search(query: LegalResearchQueryDto, subscriptionTier: string): Promise<SearchResults> {
    const startTime = Date.now();
    
    this.logger.debug(`Starting orchestrated search for: "${query.query}"`);
    
    try {
      // Get allowed sources for subscription tier
      const allowedSources = this.getAllowedSources(subscriptionTier);
      const tierLimits = TIER_LIMITATIONS[subscriptionTier];
      
      if (!tierLimits) {
        throw new Error(`Unknown subscription tier: ${subscriptionTier}`);
      }

      // Apply tier limitations
      const maxSources = Math.min(
        query.options?.maxSources || 10,
        tierLimits.maxSourcesPerQuery
      );

      // Build search query object
      const searchQuery: SearchQuery = {
        query: query.query,
        options: {
          maxResults: maxSources,
          jurisdictions: query.options?.jurisdictions,
          practiceAreas: query.options?.practiceAreas,
          timeRange: query.options?.timeRange,
          sourceTypes: query.options?.sourceTypes,
          timeout: 10000
        },
        context: {
          sessionId: query.sessionId
        }
      };

      // Execute searches in parallel
      const searchPromises = this.createSearchPromises(searchQuery, allowedSources);
      const searchResults = await Promise.allSettled(searchPromises);

      // Aggregate and process results
      const aggregatedResults = this.aggregateResults(searchResults);
      const rankedResults = this.rankAndFilterResults(aggregatedResults, searchQuery, maxSources);

      const duration = Date.now() - startTime;
      
      this.logger.debug(`Search completed in ${duration}ms, found ${rankedResults.length} results`);

      return {
        totalSources: rankedResults.length,
        sources: rankedResults.map(this.convertToSearchSource),
        metadata: {
          duration,
          cacheHit: false,
          searchStrategies: this.getSearchStrategies(searchQuery),
          sourceBreakdown: this.calculateSourceBreakdown(rankedResults)
        }
      };

    } catch (error) {
      this.logger.error(`Search orchestration failed: ${error.message}`, error.stack);
      
      // Return empty results on failure rather than throwing
      return {
        totalSources: 0,
        sources: [],
        metadata: {
          duration: Date.now() - startTime,
          cacheHit: false,
          searchStrategies: ['error'],
          sourceBreakdown: {}
        }
      };
    }
  }

  private getAllowedSources(subscriptionTier: string): string[] {
    return getSourcesForTier(subscriptionTier);
  }

  private createSearchPromises(
    searchQuery: SearchQuery, 
    allowedSources: string[]
  ): Promise<SearchSourceResult[]>[] {
    const promises: Promise<SearchSourceResult[]>[] = [];

    // CourtListener search for case law
    if (allowedSources.includes('court_listener') && 
        searchQuery.options.sourceTypes?.includes('case_law')) {
      promises.push(this.searchCourtListener(searchQuery));
    }

    // GovInfo search for statutes and regulations
    if (allowedSources.includes('gov_info') && 
        (searchQuery.options.sourceTypes?.includes('statutes') || 
         searchQuery.options.sourceTypes?.includes('regulations'))) {
      promises.push(this.searchGovInfo(searchQuery));
    }

    // Web search for news and articles using SerpApi
    if (allowedSources.includes('serpapi_search') &&
        searchQuery.options.sourceTypes?.includes('news')) {
      promises.push(this.webSearchService.search(searchQuery));
    }

    return promises;
  }

  private async searchCourtListener(searchQuery: SearchQuery): Promise<SearchSourceResult[]> {
    try {
      this.logger.debug('Searching CourtListener for case law');
      
      const results = await this.courtListenerService.searchCases({
        query: searchQuery.query,
        jurisdiction: searchQuery.options.jurisdictions?.join(','),
        filed_after: searchQuery.options.timeRange?.from,
        filed_before: searchQuery.options.timeRange?.to,
        page_size: Math.min(searchQuery.options.maxResults || 25, 50)
      });

      if (!results?.results) {
        return [];
      }

      return results.results.map((result, index) => ({
        id: `cl_${result.id}`,
        source: 'CourtListener',
        type: 'case_law',
        title: result.caseName || result.case_name || 'Untitled Case',
        citation: result.citation || '',
        court: result.court || '',
        date: result.dateFiled || result.date_filed || new Date().toISOString(),
        jurisdiction: this.extractJurisdictionFromCourt(result.court || ''),
        practiceArea: this.classifyPracticeAreaFromText(result.caseName || ''),
        url: result.absolute_url || `https://www.courtlistener.com/opinion/${result.id}/`,
        snippet: result.case_name?.substring(0, 200) || '',
        relevanceScore: this.calculateRelevanceScore(searchQuery.query, result.caseName || '', index),
        authorityScore: this.calculateAuthorityScore(result.court || ''),
        metadata: {
          citationCount: 0, // Not available in current interface
          lastUpdated: result.date_filed || new Date().toISOString(),
          documentType: 'opinion'
        }
      }));

    } catch (error) {
      this.logger.error(`CourtListener search failed: ${error.message}`);
      return [];
    }
  }

  private async searchGovInfo(searchQuery: SearchQuery): Promise<SearchSourceResult[]> {
    try {
      this.logger.debug('GovInfo search not yet implemented - returning mock results');

      // Return mock GovInfo results for development
      return [
        {
          id: 'govinfo_mock_1',
          source: 'GovInfo Mock (Development)',
          type: 'statute',
          title: 'Federal Privacy Act - 5 U.S.C. § 552a',
          citation: '5 U.S.C. § 552a',
          court: '',
          authority: 'Federal Statute',
          date: new Date().toISOString(),
          jurisdiction: 'federal',
          practiceArea: 'privacy',
          url: 'https://govinfo.gov/privacy-act',
          snippet: 'The Privacy Act of 1974 establishes a code of fair information practices that governs the collection, maintenance, use, and dissemination of information about individuals.',
          relevanceScore: 0.85,
          authorityScore: 0.95,
          metadata: {
            documentType: 'statute',
            lastUpdated: new Date().toISOString(),
            mockResult: true
          }
        }
      ];

    } catch (error) {
      this.logger.error(`GovInfo search failed: ${error.message}`);
      return [];
    }
  }

  private aggregateResults(
    searchResults: PromiseSettledResult<SearchSourceResult[]>[]
  ): SearchSourceResult[] {
    const allResults: SearchSourceResult[] = [];
    
    searchResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        allResults.push(...result.value);
      } else {
        this.logger.warn(`Search source ${index} failed: ${result.reason}`);
      }
    });

    // Remove duplicates based on URL or citation
    const uniqueResults = this.removeDuplicates(allResults);
    
    this.logger.debug(`Aggregated ${uniqueResults.length} unique results from ${allResults.length} total`);
    
    return uniqueResults;
  }

  private removeDuplicates(results: SearchSourceResult[]): SearchSourceResult[] {
    const seen = new Set<string>();
    const unique: SearchSourceResult[] = [];

    for (const result of results) {
      // Create a unique key based on URL or citation
      const key = result.citation || result.url || result.title || `result-${unique.length}`;
      let normalizedKey: string;

      try {
        normalizedKey = (key && typeof key === 'string') ? key.toLowerCase().trim() : `result-${unique.length}`;
      } catch (error) {
        normalizedKey = `result-${unique.length}`;
      }

      if (!seen.has(normalizedKey)) {
        seen.add(normalizedKey);
        unique.push(result);
      }
    }

    return unique;
  }

  private rankAndFilterResults(
    results: SearchSourceResult[], 
    searchQuery: SearchQuery, 
    maxResults: number
  ): SearchSourceResult[] {
    // Calculate combined scores and sort
    const scoredResults = results.map(result => ({
      ...result,
      combinedScore: this.calculateCombinedScore(result, searchQuery)
    }));

    // Sort by combined score (descending)
    scoredResults.sort((a, b) => b.combinedScore - a.combinedScore);

    // Apply filters based on search options
    let filteredResults = scoredResults;

    // Filter by jurisdiction if specified
    if (searchQuery.options.jurisdictions?.length) {
      filteredResults = filteredResults.filter(result => 
        searchQuery.options.jurisdictions!.includes(result.jurisdiction) ||
        result.jurisdiction === 'unknown'
      );
    }

    // Filter by practice area if specified
    if (searchQuery.options.practiceAreas?.length) {
      filteredResults = filteredResults.filter(result => 
        !result.practiceArea || 
        searchQuery.options.practiceAreas!.includes(result.practiceArea)
      );
    }

    // Return top results up to maxResults
    return filteredResults.slice(0, maxResults);
  }

  private calculateCombinedScore(result: SearchSourceResult, searchQuery: SearchQuery): number {
    // Weighted combination of relevance and authority scores
    const relevanceWeight = 0.7;
    const authorityWeight = 0.3;
    
    return (result.relevanceScore * relevanceWeight) + (result.authorityScore * authorityWeight);
  }

  private calculateRelevanceScore(query: string, title: string, rank: number): number {
    let score = 1.0;
    
    // Rank-based scoring (higher rank = lower score)
    score *= Math.max(0.1, 1.0 - (rank * 0.05));
    
    // Query term matching
    const queryTerms = query.toLowerCase().split(/\s+/);
    const titleLower = title.toLowerCase();
    const matchCount = queryTerms.filter(term => titleLower.includes(term)).length;
    
    if (matchCount > 0) {
      score *= (1 + (matchCount / queryTerms.length) * RELEVANCE_FACTORS.KEYWORD_MATCH);
    }
    
    return Math.min(1.0, score);
  }

  private calculateAuthorityScore(court: string): number {
    return AUTHORITY_SCORES[court] || AUTHORITY_SCORES.DEFAULT_CASE_LAW;
  }

  private extractJurisdictionFromCourt(court: string): string {
    const courtLower = court.toLowerCase();
    
    if (courtLower.includes('supreme court') && !courtLower.includes('state')) {
      return 'federal';
    }
    
    if (courtLower.includes('california') || courtLower.includes('cal.')) {
      return 'california';
    }
    
    if (courtLower.includes('new york') || courtLower.includes('n.y.')) {
      return 'new_york';
    }
    
    // Add more jurisdiction mappings as needed
    
    return 'unknown';
  }

  private classifyPracticeAreaFromText(text: string): string | undefined {
    const areas = classifyPracticeArea(text);
    return areas.length > 0 ? areas[0] : undefined;
  }

  private convertToSearchSource(result: SearchSourceResult) {
    return {
      id: result.id,
      type: result.type,
      title: result.title,
      citation: result.citation,
      court: result.court,
      authority: result.authority,
      date: result.date,
      jurisdiction: result.jurisdiction,
      practiceArea: result.practiceArea,
      url: result.url,
      snippet: result.snippet,
      relevanceScore: result.relevanceScore,
      authorityScore: result.authorityScore,
      metadata: result.metadata
    };
  }

  private getSearchStrategies(searchQuery: SearchQuery): string[] {
    const strategies: string[] = [];
    
    if (searchQuery.options.sourceTypes?.includes('case_law')) {
      strategies.push('case_law_search');
    }
    
    if (searchQuery.options.sourceTypes?.includes('statutes')) {
      strategies.push('statute_search');
    }
    
    if (searchQuery.options.sourceTypes?.includes('news')) {
      strategies.push('web_search');
    }
    
    if (searchQuery.options.jurisdictions?.length) {
      strategies.push('jurisdiction_filtered');
    }
    
    if (searchQuery.options.practiceAreas?.length) {
      strategies.push('practice_area_filtered');
    }
    
    return strategies;
  }

  private calculateSourceBreakdown(results: SearchSourceResult[]): { [sourceType: string]: number } {
    const breakdown: { [sourceType: string]: number } = {};
    
    results.forEach(result => {
      breakdown[result.type] = (breakdown[result.type] || 0) + 1;
    });
    
    return breakdown;
  }
}
