import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
const GoogleSearchResults = require('google-search-results-nodejs');
import { 
  SearchSourceProvider, 
  SearchQuery, 
  SearchSourceResult, 
  RateLimitInfo 
} from '../interfaces/search-source.interface';
import {
  LEGAL_SITE_FILTERS,
  EXCLUDE_PATTERNS,
  buildSearchQuery
} from '../constants/search-sources.constants';
import { classifyPracticeArea, extractJurisdictions } from '../constants/practice-areas.constants';

interface SerpApiResult {
  position: number;
  title: string;
  link: string;
  snippet: string;
  displayed_link: string;
  date?: string;
  snippet_highlighted_words?: string[];
  rich_snippet?: any;
  cached_page_link?: string;
  related_pages_link?: string;
  thumbnail?: string;
  source?: string;
}

interface SerpApiResponse {
  organic_results?: SerpApiResult[];
  news_results?: SerpApiResult[];
  related_questions?: Array<{
    question: string;
    snippet: string;
    title: string;
    link: string;
  }>;
  search_metadata?: {
    status: string;
    created_at: string;
    processed_at: string;
    total_time_taken: number;
  };
  search_parameters?: {
    engine: string;
    q: string;
    location: string;
    hl: string;
    gl: string;
  };
  search_information?: {
    query_displayed: string;
    total_results: number;
    time_taken_displayed: number;
  };
  error?: string;
}

@Injectable()
export class SerpApiEnhancedService implements SearchSourceProvider {
  private readonly logger = new Logger(SerpApiEnhancedService.name);
  private readonly apiKey: string;
  private readonly search: any;

  // Rate limiting for SerpApi
  private requestCount = 0;
  private windowStart = Date.now();
  private readonly maxRequestsPerMonth = 100; // SerpApi free tier
  private readonly windowMs = 30 * 24 * 60 * 60 * 1000; // 30 days

  constructor(private readonly configService: ConfigService) {
    this.apiKey = this.configService.get<string>('SERPAPI_API_KEY') || '';

    if (!this.apiKey) {
      this.logger.warn('SerpApi API key not configured - web search will be limited');
    } else {
      try {
        this.search = new GoogleSearchResults.GoogleSearch();
        this.logger.log('SerpApi service initialized successfully');
      } catch (error) {
        this.logger.error('Failed to initialize SerpApi service:', error.message);
      }
    }
  }

  get name(): string {
    return 'SerpApi Enhanced Search';
  }

  get type(): 'legal_database' | 'web_search' | 'news_api' {
    return 'web_search';
  }

  async search(query: SearchQuery): Promise<SearchSourceResult[]> {
    if (!this.apiKey || !this.search) {
      this.logger.warn('SerpApi not configured or initialized, skipping web search');
      return [];
    }

    try {
      // Check rate limits
      await this.checkRateLimit();

      // Build enhanced search query
      const enhancedQuery = this.buildLegalSearchQuery(query);
      
      this.logger.debug(`Performing SerpApi enhanced search for: ${enhancedQuery}`);

      // Prepare search parameters
      const searchParams = {
        api_key: this.apiKey,
        engine: 'google',
        q: enhancedQuery,
        num: Math.min(query.options.maxResults || 10, 20),
        safe: 'active',
        hl: 'en',
        gl: 'us',
        location: 'United States',
        google_domain: 'google.com',
        device: 'desktop',
        no_cache: false,
        ...this.buildDateFilter(query.options.timeRange)
      };

      // Execute search using SerpApi
      const response = await this.executeSerpApiSearch(searchParams);
      
      this.requestCount++;

      if (response.error) {
        throw new HttpException(
          `SerpApi error: ${response.error}`,
          HttpStatus.BAD_REQUEST
        );
      }

      // Process both organic and news results
      const organicResults = this.processSerpApiResults(response.organic_results || [], query, 'organic');
      const newsResults = this.processSerpApiResults(response.news_results || [], query, 'news');
      
      const allResults = [...organicResults, ...newsResults];
      
      this.logger.debug(`SerpApi enhanced search returned ${allResults.length} results`);
      
      return allResults;

    } catch (error) {
      this.logger.error(`SerpApi enhanced search failed: ${error.message}`, error.stack);
      
      if (error.response?.status === 429) {
        throw new HttpException(
          'SerpApi rate limit exceeded',
          HttpStatus.TOO_MANY_REQUESTS
        );
      }
      
      // Don't throw for web search failures - just return empty results
      return [];
    }
  }

  async isAvailable(): Promise<boolean> {
    if (!this.apiKey) {
      return false;
    }

    try {
      // Simple test query to check if service is available
      const response = await this.executeSerpApiSearch({
        api_key: this.apiKey,
        engine: 'google',
        q: 'test legal',
        num: 1
      });

      return !response.error;
    } catch (error) {
      this.logger.warn(`SerpApi availability check failed: ${error.message}`);
      return false;
    }
  }

  getRateLimit(): RateLimitInfo {
    const now = Date.now();
    
    // Reset window if needed
    if (now - this.windowStart > this.windowMs) {
      this.requestCount = 0;
      this.windowStart = now;
    }

    return {
      remaining: Math.max(0, this.maxRequestsPerMonth - this.requestCount),
      limit: this.maxRequestsPerMonth,
      resetTime: new Date(this.windowStart + this.windowMs),
      windowMs: this.windowMs
    };
  }

  private async executeSerpApiSearch(params: any): Promise<SerpApiResponse> {
    return new Promise((resolve, reject) => {
      getJson(params, (json) => {
        if (json.error) {
          reject(new Error(json.error));
        } else {
          resolve(json as SerpApiResponse);
        }
      });
    });
  }

  private async checkRateLimit(): Promise<void> {
    const rateLimit = this.getRateLimit();
    
    if (rateLimit.remaining <= 0) {
      throw new HttpException(
        'SerpApi monthly rate limit exceeded',
        HttpStatus.TOO_MANY_REQUESTS
      );
    }
  }

  private buildLegalSearchQuery(query: SearchQuery): string {
    let searchQuery = buildSearchQuery(query.query, query.options);
    
    // Add legal site filters to prioritize authoritative sources
    const siteFilters = LEGAL_SITE_FILTERS.slice(0, 5).join(' OR ');
    searchQuery += ` (${siteFilters})`;
    
    // Add exclusions
    const exclusions = EXCLUDE_PATTERNS.slice(0, 3).join(' ');
    searchQuery += ` ${exclusions}`;
    
    // Add legal-specific terms to improve relevance
    searchQuery += ' (law OR legal OR court OR statute OR regulation)';
    
    return searchQuery;
  }

  private buildDateFilter(timeRange?: { from: string; to: string }): any {
    if (!timeRange) {
      return {};
    }

    const fromDate = new Date(timeRange.from);
    const toDate = new Date(timeRange.to);
    const now = new Date();
    
    // Calculate days from 'from' date to now for SerpApi time filters
    const daysDiff = Math.floor((now.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24));
    
    // SerpApi supports various time filters
    if (daysDiff <= 1) return { tbs: 'qdr:d' }; // Past day
    if (daysDiff <= 7) return { tbs: 'qdr:w' }; // Past week
    if (daysDiff <= 30) return { tbs: 'qdr:m' }; // Past month
    if (daysDiff <= 365) return { tbs: 'qdr:y' }; // Past year
    
    // For custom date ranges, use SerpApi's date range format
    const fromFormatted = fromDate.toISOString().split('T')[0].replace(/-/g, '/');
    const toFormatted = toDate.toISOString().split('T')[0].replace(/-/g, '/');
    
    return { 
      tbs: `cdr:1,cd_min:${fromFormatted},cd_max:${toFormatted}` 
    };
  }

  private processSerpApiResults(
    results: SerpApiResult[], 
    query: SearchQuery,
    resultType: 'organic' | 'news'
  ): SearchSourceResult[] {
    if (!results || results.length === 0) {
      return [];
    }

    return results.map((item, index) => {
      const practiceAreas = classifyPracticeArea(item.title + ' ' + item.snippet);
      const jurisdictions = extractJurisdictions(item.title + ' ' + item.snippet);
      
      // Calculate relevance score
      const relevanceScore = this.calculateRelevanceScore(item, query, index, resultType);
      
      // Calculate authority score based on domain
      const authorityScore = this.calculateAuthorityScore(item.displayed_link);
      
      // Determine source type based on content and result type
      const sourceType = this.determineSourceType(item, resultType);
      
      return {
        id: `serpapi_${resultType}_${Date.now()}_${index}`,
        source: 'SerpApi Enhanced Search',
        type: sourceType,
        title: item.title,
        url: item.link,
        snippet: item.snippet,
        date: this.extractDate(item) || new Date().toISOString(),
        jurisdiction: jurisdictions[0] || 'unknown',
        practiceArea: practiceAreas[0],
        relevanceScore,
        authorityScore,
        metadata: {
          displayLink: item.displayed_link,
          searchRank: item.position,
          practiceAreas,
          jurisdictions,
          highlightedWords: item.snippet_highlighted_words,
          cachedPageLink: item.cached_page_link,
          relatedPagesLink: item.related_pages_link,
          resultType,
          thumbnail: item.thumbnail,
          source: item.source
        }
      };
    });
  }

  private calculateRelevanceScore(
    item: SerpApiResult, 
    query: SearchQuery, 
    index: number,
    resultType: 'organic' | 'news'
  ): number {
    let score = 1.0;
    
    // Position-based scoring
    score *= Math.max(0.1, 1.0 - ((item.position - 1) * 0.05));
    
    // Query term matching in title (higher weight)
    const titleMatches = this.countQueryMatches(query.query, item.title);
    score += titleMatches * 0.3;
    
    // Query term matching in snippet
    const snippetMatches = this.countQueryMatches(query.query, item.snippet);
    score += snippetMatches * 0.2;
    
    // Bonus for highlighted words (SerpApi feature)
    if (item.snippet_highlighted_words && item.snippet_highlighted_words.length > 0) {
      score += item.snippet_highlighted_words.length * 0.1;
    }
    
    // Bonus for legal domains
    if (this.isLegalDomain(item.displayed_link)) {
      score *= 1.5;
    }
    
    // Bonus for government domains
    if (item.displayed_link.includes('.gov')) {
      score *= 1.8;
    }
    
    // Bonus for rich snippets
    if (item.rich_snippet) {
      score *= 1.2;
    }
    
    // News results get slight boost for recency
    if (resultType === 'news') {
      score *= 1.1;
    }
    
    return Math.min(1.0, score);
  }

  private calculateAuthorityScore(domain: string): number {
    // Use existing authority scoring from constants
    const authorityScores = {
      'govinfo.gov': 0.95,
      'uscourts.gov': 0.9,
      'supremecourt.gov': 1.0,
      'courtlistener.com': 0.85,
      'law.cornell.edu': 0.8,
      'justia.com': 0.75,
      'law.com': 0.7,
      'americanbar.org': 0.8,
      'reuters.com': 0.75,
      'bloomberg.com': 0.75,
      'law360.com': 0.8
    };

    for (const [authorityDomain, score] of Object.entries(authorityScores)) {
      if (domain.includes(authorityDomain)) {
        return score;
      }
    }

    // Default scores
    if (domain.includes('.gov')) return 0.8;
    if (domain.includes('.edu')) return 0.7;
    if (domain.includes('law')) return 0.6;
    
    return 0.5; // Default score
  }

  private determineSourceType(
    item: SerpApiResult, 
    resultType: 'organic' | 'news'
  ): 'case_law' | 'statute' | 'regulation' | 'news' | 'article' | 'other' {
    const content = (item.title + ' ' + item.snippet).toLowerCase();
    const domain = item.displayed_link.toLowerCase();
    
    // If it's from news results, it's likely news
    if (resultType === 'news') {
      return 'news';
    }
    
    // Check for case law indicators
    if (content.includes('court') || content.includes('case') || content.includes('opinion') ||
        content.includes('judgment') || content.includes('ruling') || 
        domain.includes('courtlistener') || domain.includes('justia.com/cases')) {
      return 'case_law';
    }
    
    // Check for statute indicators
    if (content.includes('statute') || content.includes('code') || content.includes('usc') ||
        content.includes('title ') || domain.includes('uscode') || 
        domain.includes('law.cornell.edu/uscode')) {
      return 'statute';
    }
    
    // Check for regulation indicators
    if (content.includes('regulation') || content.includes('cfr') || content.includes('rule') ||
        content.includes('federal register') || domain.includes('ecfr') ||
        domain.includes('federalregister.gov')) {
      return 'regulation';
    }
    
    // Check for news indicators
    if (content.includes('news') || domain.includes('news') || 
        domain.includes('reuters') || domain.includes('bloomberg') ||
        domain.includes('law360') || domain.includes('legaltech')) {
      return 'news';
    }
    
    // Check for legal article indicators
    if (content.includes('article') || content.includes('analysis') || 
        content.includes('commentary') || domain.includes('law.com') || 
        domain.includes('americanbar.org') || domain.includes('lawreview')) {
      return 'article';
    }
    
    return 'other';
  }

  private extractDate(item: SerpApiResult): string | null {
    // SerpApi sometimes provides date directly
    if (item.date) {
      const parsedDate = new Date(item.date);
      if (!isNaN(parsedDate.getTime())) {
        return parsedDate.toISOString();
      }
    }
    
    // Try to extract date from snippet using comprehensive patterns
    const datePatterns = [
      /\b(\d{1,2}\/\d{1,2}\/\d{4})\b/, // MM/DD/YYYY
      /\b(\d{4}-\d{2}-\d{2})\b/, // YYYY-MM-DD
      /\b(\w+ \d{1,2}, \d{4})\b/, // Month DD, YYYY
      /\b(\d{1,2} \w+ \d{4})\b/, // DD Month YYYY
      /\b(\w+ \d{4})\b/, // Month YYYY
    ];
    
    for (const pattern of datePatterns) {
      const match = item.snippet.match(pattern);
      if (match) {
        const parsedDate = new Date(match[1]);
        if (!isNaN(parsedDate.getTime())) {
          return parsedDate.toISOString();
        }
      }
    }
    
    return null;
  }

  private countQueryMatches(query: string, text: string): number {
    const queryTerms = query.toLowerCase().split(/\s+/);
    const textLower = text.toLowerCase();
    
    return queryTerms.filter(term => textLower.includes(term)).length;
  }

  private isLegalDomain(domain: string): boolean {
    const legalDomains = [
      'law.cornell.edu', 'justia.com', 'findlaw.com', 'martindale.com',
      'americanbar.org', 'law.com', 'lexisnexis.com', 'westlaw.com',
      'law360.com', 'legalzoom.com', 'nolo.com'
    ];
    
    return legalDomains.some(legalDomain => domain.includes(legalDomain));
  }
}
