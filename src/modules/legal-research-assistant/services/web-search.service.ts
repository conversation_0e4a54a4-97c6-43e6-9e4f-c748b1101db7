import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { 
  SearchSourceProvider, 
  SearchQuery, 
  SearchSourceResult, 
  RateLimitInfo 
} from '../interfaces/search-source.interface';
import {
  LEGAL_SITE_FILTERS,
  EXCLUDE_PATTERNS,
  SEARCH_OPERATORS,
  calculateAuthorityScore,
  buildSearchQuery
} from '../constants/search-sources.constants';
import { classifyPracticeArea, extractJurisdictions } from '../constants/practice-areas.constants';

interface GoogleSearchResult {
  title: string;
  link: string;
  snippet: string;
  displayLink: string;
  formattedUrl: string;
  pagemap?: {
    metatags?: Array<{
      [key: string]: string;
    }>;
  };
}

interface GoogleSearchResponse {
  items?: GoogleSearchResult[];
  searchInformation?: {
    totalResults: string;
    searchTime: number;
  };
  error?: {
    code: number;
    message: string;
  };
}

@Injectable()
export class WebSearchService implements SearchSourceProvider {
  private readonly logger = new Logger(WebSearchService.name);
  private readonly httpClient: AxiosInstance;
  private readonly apiKey: string;
  private readonly searchEngineId: string;
  private readonly baseUrl = 'https://www.googleapis.com/customsearch/v1';
  
  // Rate limiting
  private requestCount = 0;
  private windowStart = Date.now();
  private readonly maxRequestsPerDay = 100; // Google Custom Search free tier limit
  private readonly windowMs = 24 * 60 * 60 * 1000; // 24 hours

  constructor(private readonly configService: ConfigService) {
    this.apiKey = this.configService.get<string>('GOOGLE_SEARCH_API_KEY') || '';
    this.searchEngineId = this.configService.get<string>('GOOGLE_SEARCH_ENGINE_ID') || '';
    
    if (!this.apiKey || !this.searchEngineId) {
      this.logger.warn('Google Custom Search API credentials not configured');
    }

    this.httpClient = axios.create({
      baseURL: this.baseUrl,
      timeout: 10000,
      headers: {
        'User-Agent': 'DocGic-Legal-Research-Assistant/1.0'
      }
    });
  }

  get name(): string {
    return 'Google Custom Search';
  }

  get type(): 'legal_database' | 'web_search' | 'news_api' {
    return 'web_search';
  }

  async search(query: SearchQuery): Promise<SearchSourceResult[]> {
    if (!this.apiKey || !this.searchEngineId) {
      this.logger.warn('Google Custom Search not configured, skipping web search');
      return [];
    }

    try {
      // Check rate limits
      await this.checkRateLimit();

      // Build enhanced search query
      const enhancedQuery = this.buildLegalSearchQuery(query);
      
      this.logger.debug(`Performing web search for: ${enhancedQuery}`);

      const response = await this.httpClient.get<GoogleSearchResponse>('', {
        params: {
          key: this.apiKey,
          cx: this.searchEngineId,
          q: enhancedQuery,
          num: Math.min(query.options.maxResults || 10, 10), // Google allows max 10 per request
          safe: 'active',
          lr: 'lang_en', // English results only
          gl: 'us', // Geolocation: United States
          dateRestrict: this.buildDateRestriction(query.options.timeRange)
        }
      });

      this.requestCount++;

      if (response.data.error) {
        throw new HttpException(
          `Google Search API error: ${response.data.error.message}`,
          HttpStatus.BAD_REQUEST
        );
      }

      const results = this.processSearchResults(response.data, query);
      
      this.logger.debug(`Web search returned ${results.length} results`);
      
      return results;

    } catch (error) {
      this.logger.error(`Web search failed: ${error.message}`, error.stack);
      
      if (error.response?.status === 429) {
        throw new HttpException(
          'Web search rate limit exceeded',
          HttpStatus.TOO_MANY_REQUESTS
        );
      }
      
      // Don't throw for web search failures - just return empty results
      return [];
    }
  }

  async isAvailable(): Promise<boolean> {
    if (!this.apiKey || !this.searchEngineId) {
      return false;
    }

    try {
      // Simple test query to check if service is available
      const response = await this.httpClient.get('', {
        params: {
          key: this.apiKey,
          cx: this.searchEngineId,
          q: 'test',
          num: 1
        },
        timeout: 5000
      });

      return !response.data.error;
    } catch (error) {
      this.logger.warn(`Web search availability check failed: ${error.message}`);
      return false;
    }
  }

  getRateLimit(): RateLimitInfo {
    const now = Date.now();
    
    // Reset window if needed
    if (now - this.windowStart > this.windowMs) {
      this.requestCount = 0;
      this.windowStart = now;
    }

    return {
      remaining: Math.max(0, this.maxRequestsPerDay - this.requestCount),
      limit: this.maxRequestsPerDay,
      resetTime: new Date(this.windowStart + this.windowMs),
      windowMs: this.windowMs
    };
  }

  private async checkRateLimit(): Promise<void> {
    const rateLimit = this.getRateLimit();
    
    if (rateLimit.remaining <= 0) {
      throw new HttpException(
        'Web search daily rate limit exceeded',
        HttpStatus.TOO_MANY_REQUESTS
      );
    }
  }

  private buildLegalSearchQuery(query: SearchQuery): string {
    let searchQuery = buildSearchQuery(query.query, query.options);
    
    // Add legal site filters to prioritize authoritative sources
    const siteFilters = LEGAL_SITE_FILTERS.slice(0, 5).join(' OR '); // Limit to avoid query length issues
    searchQuery += ` (${siteFilters})`;
    
    // Add exclusions
    const exclusions = EXCLUDE_PATTERNS.slice(0, 3).join(' '); // Limit exclusions
    searchQuery += ` ${exclusions}`;
    
    // Add legal-specific terms to improve relevance
    searchQuery += ' (law OR legal OR court OR statute OR regulation)';
    
    return searchQuery;
  }

  private buildDateRestriction(timeRange?: { from: string; to: string }): string | undefined {
    if (!timeRange) {
      return undefined;
    }

    const fromDate = new Date(timeRange.from);
    const toDate = new Date(timeRange.to);
    const now = new Date();
    
    // Calculate days from 'from' date to now
    const daysDiff = Math.floor((now.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysDiff <= 7) return 'd7';
    if (daysDiff <= 30) return 'm1';
    if (daysDiff <= 90) return 'm3';
    if (daysDiff <= 365) return 'y1';
    
    return undefined; // No restriction for older dates
  }

  private processSearchResults(
    response: GoogleSearchResponse, 
    query: SearchQuery
  ): SearchSourceResult[] {
    if (!response.items || response.items.length === 0) {
      return [];
    }

    return response.items.map((item, index) => {
      const practiceAreas = classifyPracticeArea(item.title + ' ' + item.snippet);
      const jurisdictions = extractJurisdictions(item.title + ' ' + item.snippet);
      
      // Calculate relevance score based on multiple factors
      const relevanceScore = this.calculateRelevanceScore(item, query, index);
      
      // Calculate authority score based on domain
      const authorityScore = this.calculateAuthorityScore(item.displayLink);
      
      // Determine source type based on content
      const sourceType = this.determineSourceType(item);
      
      return {
        id: `web_${Date.now()}_${index}`,
        source: 'Google Custom Search',
        type: sourceType,
        title: item.title,
        url: item.link,
        snippet: item.snippet,
        date: this.extractDate(item) || new Date().toISOString(),
        jurisdiction: jurisdictions[0] || 'unknown',
        practiceArea: practiceAreas[0],
        relevanceScore,
        authorityScore,
        metadata: {
          displayLink: item.displayLink,
          formattedUrl: item.formattedUrl,
          searchRank: index + 1,
          practiceAreas,
          jurisdictions
        }
      };
    });
  }

  private calculateRelevanceScore(
    item: GoogleSearchResult, 
    query: SearchQuery, 
    rank: number
  ): number {
    let score = 1.0;
    
    // Rank-based scoring (higher rank = lower score)
    score *= Math.max(0.1, 1.0 - (rank * 0.1));
    
    // Query term matching in title (higher weight)
    const titleMatches = this.countQueryMatches(query.query, item.title);
    score += titleMatches * 0.3;
    
    // Query term matching in snippet
    const snippetMatches = this.countQueryMatches(query.query, item.snippet);
    score += snippetMatches * 0.2;
    
    // Bonus for legal domains
    if (this.isLegalDomain(item.displayLink)) {
      score *= 1.5;
    }
    
    // Bonus for government domains
    if (item.displayLink.includes('.gov')) {
      score *= 1.8;
    }
    
    // Normalize to 0-1 range
    return Math.min(1.0, score);
  }

  private calculateAuthorityScore(domain: string): number {
    return calculateAuthorityScore('', domain);
  }

  private determineSourceType(item: GoogleSearchResult): 'case_law' | 'statute' | 'regulation' | 'news' | 'article' | 'other' {
    const content = (item.title + ' ' + item.snippet).toLowerCase();
    
    if (content.includes('court') || content.includes('case') || content.includes('opinion')) {
      return 'case_law';
    }
    
    if (content.includes('statute') || content.includes('code') || content.includes('usc')) {
      return 'statute';
    }
    
    if (content.includes('regulation') || content.includes('cfr') || content.includes('rule')) {
      return 'regulation';
    }
    
    if (content.includes('news') || item.displayLink.includes('news') || 
        item.displayLink.includes('reuters') || item.displayLink.includes('bloomberg')) {
      return 'news';
    }
    
    if (content.includes('article') || content.includes('analysis') || 
        item.displayLink.includes('law.com') || item.displayLink.includes('americanbar.org')) {
      return 'article';
    }
    
    return 'other';
  }

  private extractDate(item: GoogleSearchResult): string | null {
    // Try to extract date from metatags
    if (item.pagemap?.metatags) {
      for (const metatag of item.pagemap.metatags) {
        if (metatag['article:published_time']) {
          return metatag['article:published_time'];
        }
        if (metatag['date']) {
          return metatag['date'];
        }
        if (metatag['pubdate']) {
          return metatag['pubdate'];
        }
      }
    }
    
    // Try to extract date from snippet using regex
    const dateRegex = /\b(\d{1,2}\/\d{1,2}\/\d{4}|\d{4}-\d{2}-\d{2}|\w+ \d{1,2}, \d{4})\b/;
    const match = item.snippet.match(dateRegex);
    
    if (match) {
      const parsedDate = new Date(match[1]);
      if (!isNaN(parsedDate.getTime())) {
        return parsedDate.toISOString();
      }
    }
    
    return null;
  }

  private countQueryMatches(query: string, text: string): number {
    const queryTerms = query.toLowerCase().split(/\s+/);
    const textLower = text.toLowerCase();
    
    return queryTerms.filter(term => textLower.includes(term)).length;
  }

  private isLegalDomain(domain: string): boolean {
    const legalDomains = [
      'law.cornell.edu', 'justia.com', 'findlaw.com', 'martindale.com',
      'americanbar.org', 'law.com', 'lexisnexis.com', 'westlaw.com'
    ];
    
    return legalDomains.some(legalDomain => domain.includes(legalDomain));
  }
}
