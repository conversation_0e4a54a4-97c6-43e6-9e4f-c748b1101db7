import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AIService } from '../../ai/services/ai.service';
import {
  SynthesisResult,
  SearchResults,
  AnalysisContext,
  KeyFinding
} from '../interfaces/legal-research-result.interface';
import { ResearchOptions } from '../interfaces/legal-research-result.interface';
import { SYNTHESIS_STYLES } from '../constants/practice-areas.constants';

@Injectable()
export class LegalSynthesisService {
  private readonly logger = new Logger(LegalSynthesisService.name);

  constructor(
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => AIService))
    private readonly aiService: AIService,
  ) {}

  async synthesizeResults(
    query: string,
    searchResults: SearchResults,
    options: ResearchOptions,
    sessionContext?: {
      previousQueries: string[];
      previousFindings: string[];
    }
  ): Promise<SynthesisResult> {
    const startTime = Date.now();

    this.logger.debug(`Starting AI synthesis for query: "${query}"`);

    try {
      // Prepare analysis context
      const context = this.prepareAnalysisContext(query, searchResults, options, sessionContext);
      
      // Generate specialized legal prompt based on synthesis style
      const prompt = this.generateLegalAnalysisPrompt(context);

      // Get AI analysis with legal-specific settings
      const aiResponse = await this.aiService.generateResponse(prompt, {
        temperature: 0.3, // Lower temperature for more consistent legal analysis
        maxTokens: this.getMaxTokensForStyle(options.synthesisStyle),
        systemMessage: this.getLegalAnalysisSystemPrompt(options.synthesisStyle),
      });

      // Parse and structure the AI response
      const parsedAnalysis = this.parseAIAnalysis(aiResponse);

      // Format citations properly
      const formattedCitations = this.formatLegalCitations(
        parsedAnalysis.citations,
        searchResults.sources
      );

      // Calculate confidence score based on source quality and analysis depth
      const confidenceScore = this.calculateConfidenceScore(
        searchResults,
        parsedAnalysis,
        options
      );

      const duration = Date.now() - startTime;

      this.logger.debug(`AI synthesis completed in ${duration}ms with confidence ${confidenceScore}`);

      return {
        legalAnalysis: parsedAnalysis.analysis,
        keyFindings: parsedAnalysis.keyFindings,
        citations: formattedCitations,
        confidenceScore,
        practiceImplications: parsedAnalysis.implications,
        jurisdictionalNotes: parsedAnalysis.jurisdictionalNotes,
        recentDevelopments: parsedAnalysis.recentDevelopments,
        metadata: {
          duration,
          sourcesAnalyzed: searchResults.totalSources,
          aiModel: 'openai', // Default model name
          promptVersion: '1.0'
        }
      };

    } catch (error) {
      this.logger.error(`AI synthesis failed: ${error.message}`, error.stack);
      
      // Return a basic synthesis on failure
      return this.createFallbackSynthesis(query, searchResults, Date.now() - startTime);
    }
  }

  private prepareAnalysisContext(
    query: string,
    searchResults: SearchResults,
    options: ResearchOptions,
    sessionContext?: {
      previousQueries: string[];
      previousFindings: string[];
    }
  ): AnalysisContext {
    return {
      query,
      sources: searchResults.sources,
      options,
      sessionContext
    };
  }

  private generateLegalAnalysisPrompt(context: AnalysisContext): string {
    const { query, sources, options, sessionContext } = context;

    let prompt = `You are a legal research expert analyzing multiple sources to answer a legal research question.

RESEARCH QUESTION: ${query}

SOURCES TO ANALYZE:
${sources.map((source, index) => `
${index + 1}. ${source.title} ${source.citation ? `(${source.citation})` : ''}
   Authority: ${source.court || source.authority || 'Unknown'}
   Date: ${source.date}
   Jurisdiction: ${source.jurisdiction}
   Practice Area: ${source.practiceArea || 'General'}
   Snippet: ${source.snippet}
   URL: ${source.url}
   Authority Score: ${source.authorityScore.toFixed(2)}
   Relevance Score: ${source.relevanceScore.toFixed(2)}
`).join('\n')}`;

    // Add session context if available
    if (sessionContext?.previousQueries?.length) {
      prompt += `\n\nPREVIOUS RESEARCH CONTEXT:
Previous Questions: ${sessionContext.previousQueries.join('; ')}
Previous Key Findings: ${sessionContext.previousFindings?.join('; ') || 'None'}`;
    }

    // Add analysis requirements based on synthesis style
    prompt += this.getAnalysisRequirements(options.synthesisStyle);

    // Add response format
    prompt += `\n\nRESPONSE FORMAT (JSON):
{
  "analysis": "Comprehensive legal analysis including: (1) Overview of the legal issue and current state of law, (2) Detailed reasoning with precedent discussion and legal principles, (3) Conclusions and recommendations...",
  "keyFindings": [
    {
      "finding": "Key finding 1 with specific legal principle or fact",
      "sourceUrls": ["https://source1.com", "https://source2.com"],
      "confidence": 0.95
    },
    {
      "finding": "Key finding 2 with supporting evidence",
      "sourceUrls": ["https://source3.com"],
      "confidence": 0.88
    }
  ],
  "citations": ["Properly formatted legal citation 1", "Citation 2", "Citation 3"],
  "implications": ["Practice implication 1", "Implication 2", "Implication 3"],
  "jurisdictionalNotes": "Any important jurisdictional considerations or conflicts...",
  "recentDevelopments": "Recent changes, trends, or emerging issues in this area of law..."
}

IMPORTANT GUIDELINES:
- Use proper legal citation format (Bluebook style)
- Distinguish between binding and persuasive authority
- Note any circuit splits or jurisdictional differences
- Highlight recent developments or changes in the law
- Provide practical implications for legal practitioners
- Assess the strength and reliability of the legal authority
- Be precise and accurate in legal terminology

KEY FINDINGS REQUIREMENTS:
- Each finding should be a specific, actionable legal principle or fact
- Include 1-3 source URLs that directly support each finding
- Confidence scores should reflect: 1.0 = statutory/regulatory authority, 0.9 = binding precedent, 0.8 = persuasive authority, 0.7 = secondary sources
- Prioritize findings that are most relevant to practitioners
- Match source URLs exactly from the provided sources list`;

    return prompt;
  }

  private getAnalysisRequirements(synthesisStyle?: string): string {
    switch (synthesisStyle) {
      case SYNTHESIS_STYLES.BRIEF:
        return `\n\nANALYSIS REQUIREMENTS (BRIEF STYLE):
1. Provide a concise legal analysis (2-3 paragraphs, under 300 words)
2. Identify 3-5 key legal findings
3. Focus on the most authoritative sources
4. Highlight immediate practical implications
5. Include brief overview, key points, and conclusions`;

      case SYNTHESIS_STYLES.ANALYTICAL:
        return `\n\nANALYSIS REQUIREMENTS (ANALYTICAL STYLE):
1. Provide deep legal analysis with reasoning
2. Examine precedent and legal principles in detail
3. Analyze conflicts between sources or jurisdictions
4. Discuss policy implications and legal theory
5. Compare different approaches or interpretations
6. Assess trends and future developments`;

      case SYNTHESIS_STYLES.COMPREHENSIVE:
      default:
        return `\n\nANALYSIS REQUIREMENTS (COMPREHENSIVE STYLE):
1. Provide a thorough overview of the legal landscape
2. Identify key legal findings and trends
3. Analyze the implications for legal practice
4. Note any jurisdictional differences or conflicts
5. Highlight recent developments or changes in the law
6. Assess the strength and reliability of legal authority
7. Provide practical guidance for practitioners`;
    }
  }

  private getLegalAnalysisSystemPrompt(synthesisStyle?: string): string {
    return `You are an expert legal researcher and analyst with deep knowledge of legal research methodology, citation practices, and legal writing. Your role is to synthesize legal research results into clear, accurate, and professionally formatted analysis.

Key responsibilities:
- Analyze legal sources with appropriate weight given to authority and jurisdiction
- Use proper legal citation format (Bluebook style)
- Distinguish between primary and secondary authority
- Identify binding vs. persuasive precedent
- Note circuit splits and jurisdictional variations
- Highlight recent developments and trends
- Provide practical implications for legal practitioners

Style: ${synthesisStyle || 'comprehensive'} - adjust depth and detail accordingly.

Always maintain accuracy, objectivity, and professional legal writing standards.`;
  }

  private getMaxTokensForStyle(synthesisStyle?: string): number {
    switch (synthesisStyle) {
      case SYNTHESIS_STYLES.BRIEF:
        return 800;
      case SYNTHESIS_STYLES.ANALYTICAL:
        return 2500;
      case SYNTHESIS_STYLES.COMPREHENSIVE:
      default:
        return 2000;
    }
  }

  private parseAIAnalysis(aiResponse: string): any {
    try {
      // Try to parse as JSON first
      const parsed = JSON.parse(aiResponse);
      
      // Validate required fields
      if (!parsed.analysis || !parsed.keyFindings) {
        throw new Error('Missing required fields in AI response');
      }

      return {
        keyFindings: this.parseKeyFindings(parsed.keyFindings),
        analysis: parsed.analysis,
        citations: Array.isArray(parsed.citations) ? parsed.citations : [],
        implications: Array.isArray(parsed.implications) ? parsed.implications : [],
        jurisdictionalNotes: parsed.jurisdictionalNotes || '',
        recentDevelopments: parsed.recentDevelopments || ''
      };

    } catch (error) {
      this.logger.warn(`Failed to parse AI response as JSON: ${error.message}`);
      
      // Fallback: extract information using text parsing
      return this.parseAIResponseAsText(aiResponse);
    }
  }

  private parseKeyFindings(keyFindings: any): KeyFinding[] {
    if (!Array.isArray(keyFindings)) {
      return [];
    }

    return keyFindings.map(finding => {
      // Handle both string and object formats
      if (typeof finding === 'string') {
        return {
          finding: finding,
          sourceUrls: [],
          confidence: 0.5
        };
      }

      // Handle object format
      if (typeof finding === 'object' && finding.finding) {
        return {
          finding: finding.finding,
          sourceUrls: Array.isArray(finding.sourceUrls) ? finding.sourceUrls : [],
          confidence: typeof finding.confidence === 'number' ? finding.confidence : 0.5
        };
      }

      // Fallback for unexpected format
      return {
        finding: String(finding),
        sourceUrls: [],
        confidence: 0.5
      };
    });
  }

  private parseAIResponseAsText(response: string): any {
    // Basic text parsing as fallback
    const lines = response.split('\n').filter(line => line.trim());
    const findingLines = lines.filter(line => line.includes('•') || line.includes('-')).slice(0, 5);

    return {
      keyFindings: findingLines.map(finding => ({
        finding: finding.replace(/^[•\-\s]+/, '').trim(),
        sourceUrls: [],
        confidence: 0.5
      })),
      analysis: response,
      citations: [],
      implications: [],
      jurisdictionalNotes: '',
      recentDevelopments: ''
    };
  }

  private formatLegalCitations(citations: string[], sources: any[]): string[] {
    // Format citations according to legal standards
    return citations.map(citation => {
      // Find matching source for additional context
      const matchingSource = sources.find(source => 
        citation.includes(source.title) || 
        (source.citation && citation.includes(source.citation))
      );

      if (matchingSource?.citation) {
        return matchingSource.citation;
      }

      // Basic citation formatting
      return citation.trim();
    });
  }

  private calculateConfidenceScore(
    searchResults: SearchResults,
    parsedAnalysis: any,
    options: ResearchOptions
  ): number {
    let confidence = 0.5; // Base confidence

    // Factor in source quality
    const avgAuthorityScore = searchResults.sources.reduce(
      (sum, source) => sum + source.authorityScore, 0
    ) / searchResults.sources.length;
    confidence += avgAuthorityScore * 0.3;

    // Factor in number of sources
    const sourceCount = searchResults.sources.length;
    if (sourceCount >= 5) confidence += 0.1;
    if (sourceCount >= 10) confidence += 0.1;

    // Factor in analysis depth
    if (parsedAnalysis.keyFindings?.length >= 3) confidence += 0.1;
    if (parsedAnalysis.citations?.length >= 2) confidence += 0.1;

    // Factor in recency of sources
    const recentSources = searchResults.sources.filter(source => {
      const sourceDate = new Date(source.date);
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
      return sourceDate > oneYearAgo;
    });
    
    if (recentSources.length > 0) {
      confidence += (recentSources.length / sourceCount) * 0.1;
    }

    return Math.min(1.0, Math.max(0.1, confidence));
  }

  private createFallbackSynthesis(
    query: string,
    searchResults: SearchResults,
    duration: number
  ): SynthesisResult {
    return {
      legalAnalysis: `Research completed for: "${query}". Found ${searchResults.totalSources} relevant sources. AI synthesis service is currently unavailable. Please review the individual sources for detailed analysis.`,
      keyFindings: [
        {
          finding: `${searchResults.totalSources} sources identified`,
          sourceUrls: [],
          confidence: 0.8
        },
        {
          finding: 'AI synthesis temporarily unavailable',
          sourceUrls: [],
          confidence: 1.0
        },
        {
          finding: 'Manual review of sources recommended',
          sourceUrls: [],
          confidence: 0.9
        }
      ],
      citations: searchResults.sources
        .filter(source => source.citation)
        .map(source => source.citation!)
        .slice(0, 5),
      confidenceScore: 0.3,
      practiceImplications: ['Manual review of sources recommended'],
      metadata: {
        duration,
        sourcesAnalyzed: searchResults.totalSources,
        aiModel: 'fallback',
        promptVersion: '1.0'
      }
    };
  }
}
