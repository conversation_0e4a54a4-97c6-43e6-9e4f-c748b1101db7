/**
 * Search source configurations and constants for the Legal Research Assistant
 */

export const SEAR<PERSON>_SOURCES = {
  COURT_LISTENER: 'court_listener',
  GOV_INFO: 'gov_info',
  GOOGLE_SEARCH: 'google_search',
  LEGAL_NEWS: 'legal_news'
} as const;

export const SOURCE_TYPES = {
  LEGAL_DATABASE: 'legal_database',
  WEB_SEARCH: 'web_search',
  NEWS_API: 'news_api'
} as const;

export interface SearchSourceConfig {
  name: string;
  type: keyof typeof SOURCE_TYPES;
  enabled: boolean;
  priority: number; // 1-10, higher = more priority
  maxResults: number;
  timeout: number; // milliseconds
  rateLimit?: {
    requests: number;
    window: number; // milliseconds
  };
  subscriptionTiers: string[]; // Which tiers can access this source
}

export const SEARCH_SOURCE_CONFIGS: Record<string, SearchSourceConfig> = {
  [SEARCH_SOURCES.COURT_LISTENER]: {
    name: '<PERSON><PERSON>istener',
    type: 'LEGAL_DATABASE',
    enabled: true,
    priority: 9,
    maxResults: 25,
    timeout: 10000,
    rateLimit: {
      requests: 100,
      window: 3600000 // 1 hour
    },
    subscriptionTiers: ['law_student', 'lawyer', 'law_firm']
  },
  [SEARCH_SOURCES.GOV_INFO]: {
    name: 'GovInfo',
    type: 'LEGAL_DATABASE',
    enabled: true,
    priority: 8,
    maxResults: 20,
    timeout: 8000,
    rateLimit: {
      requests: 50,
      window: 3600000 // 1 hour
    },
    subscriptionTiers: ['law_student', 'lawyer', 'law_firm']
  },
  [SEARCH_SOURCES.GOOGLE_SEARCH]: {
    name: 'Google Custom Search',
    type: 'WEB_SEARCH',
    enabled: true,
    priority: 6,
    maxResults: 15,
    timeout: 5000,
    rateLimit: {
      requests: 100,
      window: 86400000 // 24 hours
    },
    subscriptionTiers: ['lawyer', 'law_firm'] // Not available for free tier
  },
  [SEARCH_SOURCES.LEGAL_NEWS]: {
    name: 'Legal News',
    type: 'NEWS_API',
    enabled: false, // Will be enabled when news API is integrated
    priority: 4,
    maxResults: 10,
    timeout: 5000,
    rateLimit: {
      requests: 200,
      window: 86400000 // 24 hours
    },
    subscriptionTiers: ['lawyer', 'law_firm']
  }
};

export const TIER_LIMITATIONS = {
  law_student: {
    maxSourcesPerQuery: 5,
    allowedSources: [SEARCH_SOURCES.COURT_LISTENER, SEARCH_SOURCES.GOV_INFO],
    maxQueriesPerHour: 5,
    synthesisEnabled: false
  },
  lawyer: {
    maxSourcesPerQuery: 15,
    allowedSources: Object.values(SEARCH_SOURCES),
    maxQueriesPerHour: 30,
    synthesisEnabled: true
  },
  law_firm: {
    maxSourcesPerQuery: 25,
    allowedSources: Object.values(SEARCH_SOURCES),
    maxQueriesPerHour: 100,
    synthesisEnabled: true
  }
};

export const LEGAL_SITE_FILTERS = [
  'site:courtlistener.com',
  'site:govinfo.gov',
  'site:supremecourt.gov',
  'site:uscourts.gov',
  'site:law.cornell.edu',
  'site:justia.com',
  'site:findlaw.com',
  'site:lexisnexis.com',
  'site:westlaw.com',
  'site:americanbar.org',
  'site:law.com',
  'site:legalzoom.com',
  'site:nolo.com',
  'site:martindale.com'
];

export const EXCLUDE_PATTERNS = [
  'site:wikipedia.org',
  'site:reddit.com',
  'site:quora.com',
  'site:yahoo.com',
  'site:answers.com',
  'filetype:pdf -site:govinfo.gov', // Exclude PDFs except from official sources
  'intitle:"for sale"',
  'intitle:"buy now"',
  'intitle:"advertisement"'
];

export const SEARCH_OPERATORS = {
  EXACT_PHRASE: (phrase: string) => `"${phrase}"`,
  SITE_SEARCH: (site: string) => `site:${site}`,
  EXCLUDE_SITE: (site: string) => `-site:${site}`,
  FILE_TYPE: (type: string) => `filetype:${type}`,
  DATE_RANGE: (after: string, before?: string) => {
    let operator = `after:${after}`;
    if (before) {
      operator += ` before:${before}`;
    }
    return operator;
  },
  TITLE_SEARCH: (title: string) => `intitle:"${title}"`,
  URL_SEARCH: (url: string) => `inurl:${url}`
};

export const AUTHORITY_SCORES = {
  // Federal Courts
  'Supreme Court': 1.0,
  'Court of Appeals': 0.9,
  'District Court': 0.7,
  
  // State Supreme Courts
  'California Supreme Court': 0.85,
  'New York Court of Appeals': 0.85,
  'Texas Supreme Court': 0.8,
  
  // Government Sources
  'govinfo.gov': 0.95,
  'uscourts.gov': 0.9,
  'supremecourt.gov': 1.0,
  
  // Legal Databases
  'courtlistener.com': 0.85,
  'law.cornell.edu': 0.8,
  'justia.com': 0.75,
  
  // Default scores by source type
  DEFAULT_CASE_LAW: 0.7,
  DEFAULT_STATUTE: 0.9,
  DEFAULT_REGULATION: 0.8,
  DEFAULT_NEWS: 0.4,
  DEFAULT_ARTICLE: 0.5
};

export const RELEVANCE_FACTORS = {
  EXACT_MATCH: 2.0,
  PARTIAL_MATCH: 1.5,
  KEYWORD_MATCH: 1.2,
  CONTEXT_MATCH: 1.1,
  DATE_RECENCY: {
    LAST_MONTH: 1.3,
    LAST_QUARTER: 1.2,
    LAST_YEAR: 1.1,
    OLDER: 1.0
  },
  JURISDICTION_MATCH: 1.4,
  PRACTICE_AREA_MATCH: 1.3
};

export const CACHE_SETTINGS = {
  SEARCH_RESULTS: {
    TTL: 3600, // 1 hour
    MAX_SIZE: 1000
  },
  AI_SYNTHESIS: {
    TTL: 86400, // 24 hours
    MAX_SIZE: 500
  },
  SESSION_DATA: {
    TTL: 300, // 5 minutes
    MAX_SIZE: 2000
  }
};

export const ERROR_MESSAGES = {
  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded for search source',
  SOURCE_UNAVAILABLE: 'Search source is currently unavailable',
  INSUFFICIENT_CREDITS: 'Insufficient credits for this operation',
  FEATURE_NOT_AVAILABLE: 'Feature not available in your subscription tier',
  INVALID_QUERY: 'Query must be at least 10 characters long',
  SESSION_NOT_FOUND: 'Research session not found',
  UNAUTHORIZED_ACCESS: 'Unauthorized access to research session'
};

// Helper functions
export function getSourceConfig(sourceName: string): SearchSourceConfig | null {
  return SEARCH_SOURCE_CONFIGS[sourceName] || null;
}

export function getEnabledSources(): string[] {
  return Object.entries(SEARCH_SOURCE_CONFIGS)
    .filter(([_, config]) => config.enabled)
    .map(([name, _]) => name);
}

export function getSourcesForTier(tier: string): string[] {
  const tierLimits = TIER_LIMITATIONS[tier];
  if (!tierLimits) return [];
  
  return tierLimits.allowedSources.filter(source => {
    const config = SEARCH_SOURCE_CONFIGS[source];
    return config && config.enabled && config.subscriptionTiers.includes(tier);
  });
}

export function calculateAuthorityScore(court: string, source: string): number {
  // Check for specific court authority scores
  if (AUTHORITY_SCORES[court]) {
    return AUTHORITY_SCORES[court];
  }
  
  // Check for source-based authority scores
  if (AUTHORITY_SCORES[source]) {
    return AUTHORITY_SCORES[source];
  }
  
  // Default scores based on source type
  if (source.includes('gov')) {
    return AUTHORITY_SCORES.DEFAULT_STATUTE;
  }
  
  return AUTHORITY_SCORES.DEFAULT_CASE_LAW;
}

export function buildSearchQuery(
  query: string, 
  options: {
    jurisdictions?: string[];
    practiceAreas?: string[];
    timeRange?: { from: string; to: string };
    sourceTypes?: string[];
  }
): string {
  let searchQuery = query;
  
  // Add jurisdiction filters
  if (options.jurisdictions?.length) {
    const jurisdictionTerms = options.jurisdictions
      .map(j => `"${j.replace('_', ' ')}"`)
      .join(' OR ');
    searchQuery += ` (${jurisdictionTerms})`;
  }
  
  // Add practice area terms
  if (options.practiceAreas?.length) {
    const practiceTerms = options.practiceAreas
      .map(area => `"${area.replace('_', ' ')}"`)
      .join(' OR ');
    searchQuery += ` (${practiceTerms})`;
  }
  
  // Add date range
  if (options.timeRange) {
    searchQuery += ` ${SEARCH_OPERATORS.DATE_RANGE(
      options.timeRange.from, 
      options.timeRange.to
    )}`;
  }
  
  return searchQuery.trim();
}
