# Legal Research Assistant Module

## 🎯 Overview

The Legal Research Assistant is a comprehensive AI-powered research tool that provides Perplexity-like functionality specifically tailored for legal professionals. It combines real-time search across multiple legal databases with advanced AI analysis to deliver comprehensive, cited legal research.

## 🚀 Features

### Core Capabilities
- **Multi-source Legal Search**: Searches across CourtListener, GovInfo, and SerpApi-powered web sources
- **AI-Powered Synthesis**: Comprehensive analysis with proper legal citations
- **Conversational Interface**: Context-aware follow-up questions and iterative research
- **Session Management**: Persistent research sessions with history and sharing

### Advanced Features
- **Jurisdiction Filtering**: Focus on relevant legal jurisdictions
- **Practice Area Classification**: Categorize results by legal practice areas
- **Citation Intelligence**: Automatic legal citation formatting and verification
- **Confidence Scoring**: AI confidence levels for generated analysis

## 📋 API Endpoints

### Research Operations
- `POST /legal-research-assistant/research/query` - Perform legal research query
- `POST /legal-research-assistant/research/follow-up` - Ask follow-up questions

### Session Management
- `POST /legal-research-assistant/sessions` - Create research session
- `GET /legal-research-assistant/sessions` - List research sessions
- `GET /legal-research-assistant/sessions/:id` - Get specific session
- `PUT /legal-research-assistant/sessions/:id` - Update session
- `DELETE /legal-research-assistant/sessions/:id` - Delete session

### Analytics
- `GET /legal-research-assistant/analytics` - Get research analytics
- `GET /legal-research-assistant/sessions/:id/analytics` - Get session analytics

## 💳 Subscription Integration

### Tier Access
| Feature | Law Student | Lawyer | Law Firm |
|---------|-------------|--------|----------|
| **Max Sources** | 5 per query | 15 per query | 25 per query |
| **AI Synthesis** | ❌ | ✅ | ✅ |
| **Session History** | 7 days | 90 days | Unlimited |
| **Shared Sessions** | ❌ | ❌ | ✅ |
| **Analytics** | ❌ | Basic | Advanced |
| **Rate Limit** | 5/hour | 30/hour | 100/hour |

### Credit Costs
- **Basic Search**: 1 credit per query
- **AI Synthesis**: 3 credits per analysis
- **Follow-up Questions**: 1 credit each
- **Session Management**: Free (CRUD operations)

## 🔧 Configuration

### Environment Variables
```bash
# SerpApi (recommended for enhanced web search)
SERPAPI_API_KEY=your-serpapi-api-key

# Google Custom Search API (legacy fallback)
GOOGLE_SEARCH_API_KEY=your-google-custom-search-api-key
GOOGLE_SEARCH_ENGINE_ID=your-google-custom-search-engine-id
```

### Feature Flags
The module requires the following features to be enabled in subscription tiers:
- `legal_research_assistant` - Basic research functionality
- `legal_research_synthesis` - AI-powered analysis
- `legal_research_followup` - Follow-up questions

## 🏗️ Architecture

### Services
- **LegalResearchAssistantService** - Main orchestrator service
- **LegalSearchOrchestratorService** - Coordinates searches across sources
- **WebSearchService** - SerpApi integration with Google Custom Search fallback
- **LegalSynthesisService** - AI-powered analysis and synthesis
- **ResearchSessionService** - Session management and persistence

### Data Sources
- **CourtListener** - Case law and legal opinions
- **GovInfo** - Federal statutes and regulations
- **SerpApi** - Enhanced web search with legal news and authoritative content
- **Google Custom Search** - Legacy web search support

### Database Schema
- **ResearchSession** - MongoDB collection for session persistence
- Indexes for performance optimization
- Text search capabilities

## 📊 Usage Examples

### Basic Research Query
```typescript
const query = {
  query: "What are the recent developments in data privacy law in California?",
  options: {
    includeSynthesis: true,
    maxSources: 10,
    jurisdictions: ["california", "federal"],
    practiceAreas: ["privacy", "data_protection"],
    sourceTypes: ["case_law", "statutes", "news"]
  }
};

const result = await researchService.performResearch(query, orgId, userId);
```

### Follow-up Question
```typescript
const followUp = {
  sessionId: "session_123",
  question: "How do these laws affect small businesses?",
  options: {
    includeSynthesis: true,
    focusOnPrevious: true
  }
};

const result = await researchService.askFollowUp(followUp, orgId, userId);
```

### Session Management
```typescript
// Create session
const session = await sessionService.createSession({
  title: "California Privacy Law Research",
  description: "Research for client advisory",
  tags: ["privacy", "california", "ccpa"]
}, orgId, userId);

// List sessions
const sessions = await sessionService.listSessions({
  page: 1,
  limit: 20,
  tags: "privacy,california"
}, orgId, userId);
```

## 🧪 Testing

### Running Tests
```bash
# Unit tests
npm run test src/modules/legal-research-assistant

# Integration tests
npm run test:e2e legal-research-assistant

# Coverage
npm run test:cov src/modules/legal-research-assistant
```

### Test Coverage
- Service unit tests with mocked dependencies
- Controller integration tests
- End-to-end API tests
- Performance benchmarks

## 🔒 Security & Compliance

### Data Privacy
- Query sanitization before external API calls
- Encrypted caching of search results
- Tenant isolation for all operations
- Audit logging for compliance

### Rate Limiting
- Per-tier rate limits enforced
- External API rate limit management
- Graceful degradation on failures

### Legal Compliance
- Proper attribution of all sources
- Compliance with legal database terms of service
- Copyright and fair use guidelines

## 📈 Performance

### Optimization Features
- **Parallel Processing**: Searches execute simultaneously across sources
- **Intelligent Caching**: Results cached based on query similarity
- **Connection Pooling**: Reused connections to external APIs
- **Lazy Loading**: Additional sources loaded on demand

### Monitoring
- Response time tracking
- Error rate monitoring
- Credit usage analytics
- Source reliability metrics

## 🚀 Deployment

### Prerequisites
- MongoDB for session storage
- Redis for caching (optional but recommended)
- SerpApi API key (recommended for enhanced search)
- Google Custom Search API credentials (fallback)
- CourtListener API access
- GovInfo API access (optional)

### Environment Setup
1. Configure environment variables
2. Set up subscription tier features
3. Configure credit costs
4. Enable feature flags
5. Set up monitoring and logging

## 📚 Documentation

- **API Documentation**: See `docs/api/legal-research-assistant-api.md`
- **Implementation Plan**: See `docs/legal-research-assistant-plan.md`
- **Technical Specs**: See `docs/technical-specs/legal-research-assistant-spec.md`

## 🤝 Contributing

### Development Guidelines
1. Follow existing code patterns and conventions
2. Add comprehensive tests for new features
3. Update documentation for API changes
4. Consider subscription tier implications
5. Test with different data sources

### Code Quality
- TypeScript strict mode enabled
- ESLint and Prettier configured
- Comprehensive error handling
- Proper logging and monitoring

---

**Module Version**: 1.0.0  
**Last Updated**: December 2024  
**Status**: Production Ready
