/**
 * Core interfaces for Legal Research Assistant results and data structures
 */

export interface SearchSource {
  id: string;
  type: 'case_law' | 'statute' | 'regulation' | 'news' | 'article' | 'other';
  title: string;
  citation?: string;
  court?: string;
  authority?: string;
  date: string;
  jurisdiction: string;
  practiceArea?: string;
  url: string;
  snippet: string;
  relevanceScore: number; // 0-1
  authorityScore: number; // 0-1
  metadata?: {
    citationCount?: number;
    lastUpdated?: string;
    documentType?: string;
    [key: string]: any;
  };
}

export interface SearchResults {
  totalSources: number;
  sources: SearchSource[];
  metadata: {
    duration: number; // milliseconds
    cacheHit: boolean;
    searchStrategies: string[];
    sourceBreakdown?: {
      [sourceType: string]: number;
    };
  };
}

export interface KeyFinding {
  finding: string;
  sourceUrls: string[];
  confidence?: number; // 0-1, optional confidence for this specific finding
}

export interface SynthesisResult {
  legalAnalysis: string; // Comprehensive legal analysis including overview, reasoning, and conclusions
  keyFindings: KeyFinding[];
  citations: string[];
  confidenceScore: number; // 0-1
  practiceImplications: string[];
  jurisdictionalNotes?: string;
  recentDevelopments?: string;
  metadata: {
    duration: number; // milliseconds
    sourcesAnalyzed: number;
    aiModel: string;
    promptVersion?: string;
  };
}

export interface LegalResearchResult {
  queryId: string;
  sessionId?: string;
  query: string;
  searchResults: SearchResults;
  aiSynthesis?: SynthesisResult;
  followUpSuggestions: string[];
  metadata: {
    searchDuration: number;
    synthesisDuration: number;
    totalDuration: number;
    creditsUsed: number;
    timestamp: Date;
  };
}

export interface FollowUpResult {
  queryId: string;
  sessionId: string;
  question: string;
  contextualAnalysis: string;
  searchResults: SearchResults;
  aiSynthesis?: SynthesisResult;
  followUpSuggestions: string[];
  connectionToPrevious: string;
  metadata: {
    creditsUsed: number;
    timestamp: Date;
  };
}

export interface ResearchAnalytics {
  period: string;
  summary: {
    totalQueries: number;
    totalSessions: number;
    totalCreditsUsed: number;
    averageQueriesPerSession: number;
    averageCreditsPerQuery: number;
  };
  trends: {
    queryVolume: Array<{
      date: string;
      queries: number;
      credits: number;
    }>;
  };
  topPracticeAreas: Array<{
    area: string;
    queries: number;
    percentage: number;
  }>;
  topJurisdictions: Array<{
    jurisdiction: string;
    queries: number;
    percentage: number;
  }>;
}

export interface SearchSourceConfig {
  name: string;
  type: 'legal_database' | 'web_search' | 'news_api';
  enabled: boolean;
  priority: number; // 1-10, higher = more priority
  maxResults: number;
  timeout: number; // milliseconds
  rateLimit?: {
    requests: number;
    window: number; // milliseconds
  };
}

export interface ResearchOptions {
  includeSynthesis?: boolean;
  maxSources?: number;
  jurisdictions?: string[];
  practiceAreas?: string[];
  timeRange?: {
    from: string;
    to: string;
  };
  sourceTypes?: Array<'case_law' | 'statutes' | 'regulations' | 'news'>;
  synthesisStyle?: 'brief' | 'comprehensive' | 'analytical';
  focusOnPrevious?: boolean; // For follow-up queries
}

export interface AnalysisContext {
  query: string;
  sources: SearchSource[];
  options: ResearchOptions;
  sessionContext?: {
    previousQueries: string[];
    previousFindings: string[];
  };
}
