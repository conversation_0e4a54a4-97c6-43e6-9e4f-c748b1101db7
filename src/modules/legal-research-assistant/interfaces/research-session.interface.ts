/**
 * Interfaces for research session management
 */

export interface ResearchQuery {
  queryId: string;
  query?: string;
  question?: string; // For follow-up queries
  timestamp: Date;
  creditsUsed: number;
  resultSummary: string;
  type: 'initial' | 'followup';
  metadata?: {
    sourcesFound: number;
    synthesisGenerated: boolean;
    responseTime: number;
  };
  // Complete research results for session history
  searchResults?: {
    totalSources: number;
    sources: Array<{
      id: string;
      type: string;
      title: string;
      citation?: string;
      court?: string;
      authority?: string;
      date: string;
      jurisdiction: string;
      practiceArea?: string;
      url: string;
      snippet: string;
      relevanceScore: number;
      authorityScore: number;
    }>;
    metadata: {
      duration: number;
      cacheHit: boolean;
      searchStrategies: string[];
      sourceBreakdown?: { [sourceType: string]: number };
    };
  };
  aiSynthesis?: {
    legalAnalysis: string;
    keyFindings: string[];
    citations: string[];
    confidenceScore: number;
    practiceImplications: string[];
    jurisdictionalNotes?: string;
    recentDevelopments?: string;
    metadata: {
      duration: number;
      sourcesAnalyzed: number;
      aiModel: string;
      promptVersion?: string;
    };
  };
  followUpSuggestions?: string[];
}

export interface ResearchSession {
  sessionId: string;
  organizationId: string;
  userId: string;
  title: string;
  description?: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  isShared: boolean;
  queryCount: number;
  queries: ResearchQuery[];
  totalCreditsUsed: number;
  metadata?: {
    lastAccessedAt?: Date;
    sharedWith?: string[];
    collaborators?: string[];
    [key: string]: any;
  };
}

export interface SessionSummary {
  sessionId: string;
  title: string;
  description?: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  queryCount: number;
  totalCreditsUsed: number;
  isShared: boolean;
  lastQuery?: {
    query: string;
    timestamp: Date;
  };
}

export interface SessionAnalytics {
  sessionId: string;
  totalQueries: number;
  totalCreditsUsed: number;
  averageCreditsPerQuery: number;
  sessionDuration: number; // minutes
  topPracticeAreas: string[];
  topJurisdictions: string[];
  queryTypes: {
    initial: number;
    followup: number;
  };
  synthesisGenerated: number;
  averageResponseTime: number; // seconds
}

export interface SessionCollaboration {
  sessionId: string;
  sharedWith: Array<{
    userId: string;
    email: string;
    role: 'viewer' | 'collaborator' | 'owner';
    sharedAt: Date;
  }>;
  permissions: {
    canEdit: boolean;
    canShare: boolean;
    canDelete: boolean;
  };
}

export interface SessionTemplate {
  templateId: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  initialQueries: string[];
  suggestedPracticeAreas: string[];
  suggestedJurisdictions: string[];
  isPublic: boolean;
  createdBy: string;
  usageCount: number;
  metadata?: {
    difficulty: 'beginner' | 'intermediate' | 'advanced';
    estimatedDuration: number; // minutes
    targetAudience: string[];
  };
}
