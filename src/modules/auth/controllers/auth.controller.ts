import { 
  Controller, 
  Post, 
  Body, 
  UseGuards, 
  Get, 
  UnauthorizedException,
  Req,
  Res,
  HttpStatus,
  Query,
  BadRequestException,
  NotFoundException
} from '@nestjs/common';
import { AuthService } from '../services/auth.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { GoogleAuthGuard } from '../guards/google-auth.guard';
import { Public } from '../decorators/public.decorator';
import { LoginDto } from '../dto/auth.dto';
import { AuthResponseDto, UserResponseDto } from '../dto/auth-response.dto';
import { CreateUserDto } from '../dto/user.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { GoogleAuthUrlDto } from '../dto/google-auth.dto';
import { AuthGuard } from '@nestjs/passport';
import { ResendVerificationDto, VerificationResponseDto } from '../dto/verification.dto';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Public()
  @Post('login')
  @ApiOperation({ summary: 'Login with email and password' })
  @ApiResponse({
    status: 200,
    description: 'Login successful',
    type: AuthResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  async login(@Body() loginDto: LoginDto): Promise<AuthResponseDto> {
    const user = await this.authService.validateUser(
      loginDto.email,
      loginDto.password
,
    );
    console.log('Validated user:', user);
    
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }
    
    return this.authService.login(user);
  }

  @Public()
  @Post('register')
  @ApiOperation({ summary: 'Register a new user' })
  @ApiResponse({
    status: 201,
    description: 'Registration successful',
    type: AuthResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async register(@Body() dto: CreateUserDto): Promise<AuthResponseDto> {
    return this.authService.register(dto);
  }

  @Public()
  @Post('validate-token')
  @ApiOperation({ summary: 'Validate JWT token' })
  @ApiResponse({ status: 200, description: 'Token is valid' })
  @ApiResponse({ status: 401, description: 'Invalid token' })
  async validateToken(@Body('token') token: string): Promise<any> {
    return this.authService.validateToken(token);
  }

  @Public()
  @Get('google')
  @UseGuards(GoogleAuthGuard)
  @ApiOperation({ summary: 'Initiate Google OAuth authentication' })
  @ApiResponse({ status: 302, description: 'Redirects to Google login' })
  async googleAuth() {
    // This endpoint initiates the Google OAuth flow
    // The actual redirect is handled by Passport
  }

  @Public()
  @Get('google/callback')
  @UseGuards(GoogleAuthGuard)
  @ApiOperation({ summary: 'Handle Google OAuth callback' })
  @ApiResponse({ status: 302, description: 'Redirects after successful authentication' })
  async googleAuthCallback(@Req() req: Request, @Res() res: Response) {
    // After successful Google authentication, login the user and redirect
    const user = req.user as any; // Cast to any to avoid type issues with Express.User
    const authResponse = await this.authService.login(user);
    
    // Redirect to the frontend with the token
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    res.redirect(`${frontendUrl}/auth/google-callback?token=${authResponse.token}`);
  }

  @Public()
  @Get('google/url')
  @ApiOperation({ summary: 'Get Google OAuth URL' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns Google OAuth URL',
    type: GoogleAuthUrlDto 
  })
  async getGoogleAuthUrl(): Promise<GoogleAuthUrlDto> {
    const url = await this.authService.getGoogleAuthUrl();
    return { url };
  }

  @Public()
  @Get('verify-email')
  @ApiOperation({ summary: 'Verify email address' })
  @ApiResponse({
    status: 200,
    description: 'Email verified successfully',
    type: VerificationResponseDto
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Invalid or expired token' 
  })
  async verifyEmail(@Query('token') token: string): Promise<VerificationResponseDto> {
    if (!token) {
      throw new BadRequestException('Verification token is required');
    }
    
    const success = await this.authService.verifyEmail(token);
    
    if (!success) {
      throw new BadRequestException('Invalid or expired verification token');
    }
    
    return { 
      success: true,
      message: 'Email verified successfully'
    };
  }

  @Public()
  @Post('resend-verification')
  @ApiOperation({ summary: 'Resend verification email' })
  @ApiResponse({
    status: 200,
    description: 'Verification email sent',
    type: VerificationResponseDto
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({ status: 400, description: 'Email already verified' })
  async resendVerification(@Body() resendVerificationDto: ResendVerificationDto): Promise<VerificationResponseDto> {
    if (!resendVerificationDto.email) {
      throw new BadRequestException('Email is required');
    }
    
    try {
      const success = await this.authService.resendVerificationEmail(resendVerificationDto.email);
      
      return { 
        success: true,
        message: 'Verification email sent successfully'
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to send verification email');
    }
  }
}
