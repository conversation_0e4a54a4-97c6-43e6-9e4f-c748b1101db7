import { createParamDecorator, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { TenantContextService } from '../services/tenant-context.service';

interface JwtPayload {
  sub: string;
  email: string;
  organizationId: string;
  role: string;
}

/**
 * Decorator that extracts user information from the request context
 * Usage: @User() userId: string or @User('sub') userId: string
 */
export const User = createParamDecorator(
  (data: string | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    
    // Try to get the user from the tenant context first
    if (request.tenantContext) {
      const tenantContext = request.tenantContext as TenantContextService;
      const userId = tenantContext.getCurrentUserId();
      
      if (!userId) {
        throw new UnauthorizedException('User not found in tenant context');
      }
      
      // If no specific property is requested, return the user ID
      if (!data) {
        return userId;
      }
      
      // For now, we only support 'sub' as a property, which is the user ID
      if (data === 'sub') {
        return userId;
      }
      
      throw new Error(`Unsupported user property: ${data}`);
    }
    
    // Fallback to request.user if tenant context is not available
    if (request.user) {
      if (data) {
        return request.user[data];
      }
      return request.user;
    }
    
    throw new UnauthorizedException('User not found in request');
  },
);
