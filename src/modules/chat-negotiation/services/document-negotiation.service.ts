import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ContractAnalysis } from '../../documents/schemas/contract-analysis.schema';
import { DocumentModel } from '../../documents/schemas/document.schema';
import { ChatNegotiationSession } from '../schemas/chat-negotiation-session.schema';
// import { AIService } from '../../../shared/services/ai.service'; // TODO: Add when AI service is available

export interface DocumentNegotiationScenario {
  id: string;
  title: string;
  description: string;
  contractType: string;
  focusAreas: string[];
  negotiationPoints: NegotiationPoint[];
  difficulty: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  estimatedDuration: number; // minutes
  aiPersonalityRecommendation: {
    aggressiveness: number;
    flexibility: number;
    riskTolerance: number;
    communicationStyle: string;
  };
}

export interface NegotiationPoint {
  id: string;
  category: string;
  issue: string;
  currentTerm: string;
  suggestedImprovement: string;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  priority: number;
  negotiationStrategy: string;
  fallbackOptions: string[];
}

@Injectable()
export class DocumentNegotiationService {
  private readonly logger = new Logger(DocumentNegotiationService.name);

  constructor(
    @InjectModel(ContractAnalysis.name)
    private readonly analysisModel: Model<ContractAnalysis>,
    @InjectModel(DocumentModel.name)
    private readonly documentModel: Model<DocumentModel>,
    @InjectModel(ChatNegotiationSession.name)
    private readonly sessionModel: Model<ChatNegotiationSession>,
    // private readonly aiService: AIService, // TODO: Add when AI service is available
  ) {}

  /**
   * Create a negotiation scenario from a contract analysis
   */
  async createScenarioFromAnalysis(
    analysisId: string,
    organizationId: string,
  ): Promise<DocumentNegotiationScenario> {
    this.logger.log(`Creating negotiation scenario from analysis: ${analysisId}`);

    // Get the contract analysis
    const analysis = await this.analysisModel.findOne({
      id: analysisId,
      organizationId,
    }).exec();

    if (!analysis) {
      throw new NotFoundException('Contract analysis not found');
    }

    // Get the source document
    const document = await this.documentModel.findById(analysis.contractId).exec();
    if (!document) {
      throw new NotFoundException('Source document not found');
    }

    // Extract negotiation points from deviations
    const negotiationPoints = await this.extractNegotiationPoints(analysis);

    // Generate AI personality recommendation based on analysis
    const aiPersonalityRecommendation = this.generateAIPersonalityRecommendation(analysis);

    // Determine difficulty based on analysis complexity
    const difficulty = this.determineDifficulty(analysis);

    const scenario: DocumentNegotiationScenario = {
      id: `doc-scenario-${analysisId}`,
      title: `Negotiate: ${analysis.metadata.contractTitle || document.metadata?.originalName || 'Contract'}`,
      description: this.generateScenarioDescription(analysis),
      contractType: analysis.metadata.contractType || 'GENERAL',
      focusAreas: this.extractFocusAreas(analysis),
      negotiationPoints,
      difficulty,
      estimatedDuration: this.estimateDuration(negotiationPoints),
      aiPersonalityRecommendation,
    };

    this.logger.log(`Created scenario with ${negotiationPoints.length} negotiation points`);
    return scenario;
  }

  /**
   * Create a negotiation session from a document analysis
   */
  async createSessionFromDocument(
    analysisId: string,
    organizationId: string,
    userId: string,
    aiPersonality?: any,
  ): Promise<ChatNegotiationSession> {
    const scenario = await this.createScenarioFromAnalysis(analysisId, organizationId);
    const analysis = await this.analysisModel.findOne({
      id: analysisId,
      organizationId,
    }).exec();

    // Use provided AI personality or the recommended one
    const finalAiPersonality = aiPersonality || {
      characterId: 'contract_specialist',
      ...scenario.aiPersonalityRecommendation,
    };

    // Create the negotiation session
    const session = new this.sessionModel({
      userId,
      organizationId,
      scenarioId: scenario.id,
      sourceDocumentId: analysis.contractId,
      sourceAnalysisId: analysisId,
      documentContext: {
        contractType: analysis.metadata.contractType,
        contractTitle: analysis.metadata.contractTitle,
        analysisScore: analysis.overallScore,
        riskLevel: analysis.riskLevel,
        totalDeviations: analysis.summary.totalDeviations,
        keyFindings: analysis.summary.keyFindings,
      },
      status: 'active',
      currentRound: 1,
      extractedTerms: {},
      relationshipMetrics: {
        trust: 50,
        respect: 50,
        pressure: 20,
      },
      score: 0,
      aiPersonality: finalAiPersonality,
      negotiationSessionId: `neg-${Date.now()}`,
      totalMessages: 0,
      aiResponseTime: 0,
    });

    await session.save();
    this.logger.log(`Created document-based negotiation session: ${session.id}`);
    return session;
  }

  /**
   * Get negotiation context for AI responses
   */
  async getNegotiationContext(sessionId: string): Promise<string> {
    const session = await this.sessionModel.findOne({ id: sessionId }).exec();
    if (!session || !session.sourceAnalysisId) {
      return '';
    }

    const analysis = await this.analysisModel.findOne({
      id: session.sourceAnalysisId,
    }).exec();

    if (!analysis) {
      return '';
    }

    const context = `
Contract Analysis Context:
- Contract Type: ${analysis.metadata.contractType}
- Overall Score: ${analysis.overallScore}/100
- Risk Level: ${analysis.riskLevel}
- Total Deviations: ${analysis.summary.totalDeviations}

Key Issues to Negotiate:
${analysis.deviations
  .filter(d => d.severity === 'HIGH' || d.severity === 'CRITICAL')
  .slice(0, 5)
  .map(d => `- ${d.ruleName}: ${d.clauseText.substring(0, 100)}...`)
  .join('\n')}

Negotiation Focus:
${analysis.summary.keyFindings.slice(0, 3).map(f => `- ${f}`).join('\n')}
    `.trim();

    return context;
  }

  private async extractNegotiationPoints(analysis: ContractAnalysis): Promise<NegotiationPoint[]> {
    const points: NegotiationPoint[] = [];

    // Convert high-priority deviations to negotiation points
    const highPriorityDeviations = analysis.deviations
      .filter(d => d.severity === 'HIGH' || d.severity === 'CRITICAL')
      .slice(0, 8); // Limit to top 8 issues

    for (let i = 0; i < highPriorityDeviations.length; i++) {
      const deviation = highPriorityDeviations[i];
      
      const point: NegotiationPoint = {
        id: `point-${i + 1}`,
        category: this.categorizeDeviation(deviation.ruleName),
        issue: deviation.ruleName,
        currentTerm: deviation.clauseText.substring(0, 200),
        suggestedImprovement: deviation.suggestedText || 'Negotiate more favorable terms',
        riskLevel: deviation.severity as any,
        priority: i + 1,
        negotiationStrategy: this.generateNegotiationStrategy(deviation),
        fallbackOptions: this.generateFallbackOptions(deviation),
      };

      points.push(point);
    }

    return points;
  }

  private generateAIPersonalityRecommendation(analysis: ContractAnalysis) {
    // Adjust AI personality based on contract risk and complexity
    const baseAggressiveness = 0.5;
    const baseFlexibility = 0.6;
    const baseRiskTolerance = 0.4;

    // Higher risk contracts = more aggressive AI
    const riskMultiplier = analysis.riskLevel === 'CRITICAL' ? 0.3 : 
                          analysis.riskLevel === 'HIGH' ? 0.2 : 
                          analysis.riskLevel === 'MEDIUM' ? 0.1 : 0;

    // More deviations = less flexible AI
    const deviationMultiplier = Math.min(analysis.summary.totalDeviations / 20, 0.3);

    return {
      aggressiveness: Math.min(baseAggressiveness + riskMultiplier, 1.0),
      flexibility: Math.max(baseFlexibility - deviationMultiplier, 0.1),
      riskTolerance: Math.max(baseRiskTolerance - riskMultiplier, 0.1),
      communicationStyle: analysis.riskLevel === 'CRITICAL' ? 'DIRECT' : 'ANALYTICAL',
    };
  }

  private determineDifficulty(analysis: ContractAnalysis): 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' {
    const score = analysis.overallScore;
    const deviations = analysis.summary.totalDeviations;
    const criticalIssues = analysis.summary.criticalDeviations;

    if (score > 80 && deviations < 5) return 'BEGINNER';
    if (score > 60 && criticalIssues < 3) return 'INTERMEDIATE';
    return 'ADVANCED';
  }

  private generateScenarioDescription(analysis: ContractAnalysis): string {
    return `Practice negotiating the key issues identified in this ${analysis.metadata.contractType} contract. ` +
           `The analysis found ${analysis.summary.totalDeviations} deviations with an overall score of ${analysis.overallScore}/100. ` +
           `Focus on addressing the ${analysis.summary.criticalDeviations + analysis.summary.highRiskDeviations} high-priority issues ` +
           `to improve contract terms and reduce risk.`;
  }

  private extractFocusAreas(analysis: ContractAnalysis): string[] {
    const areas = new Set<string>();
    
    analysis.deviations.forEach(deviation => {
      areas.add(this.categorizeDeviation(deviation.ruleName));
    });

    return Array.from(areas).slice(0, 5);
  }

  private categorizeDeviation(ruleName: string): string {
    const name = ruleName.toLowerCase();
    if (name.includes('payment') || name.includes('fee')) return 'Payment Terms';
    if (name.includes('termination') || name.includes('term')) return 'Contract Duration';
    if (name.includes('liability') || name.includes('indemnity')) return 'Risk & Liability';
    if (name.includes('intellectual') || name.includes('ip')) return 'Intellectual Property';
    if (name.includes('confidential') || name.includes('nda')) return 'Confidentiality';
    if (name.includes('delivery') || name.includes('performance')) return 'Performance';
    if (name.includes('dispute') || name.includes('arbitration')) return 'Dispute Resolution';
    return 'General Terms';
  }

  private generateNegotiationStrategy(deviation: any): string {
    const strategies = [
      'Request modification to reduce risk exposure',
      'Propose alternative language that balances interests',
      'Negotiate reciprocal obligations',
      'Seek limitation or cap on liability',
      'Request additional protections or guarantees',
    ];
    return strategies[Math.floor(Math.random() * strategies.length)];
  }

  private generateFallbackOptions(deviation: any): string[] {
    return [
      'Accept current terms with additional protections',
      'Propose compromise language',
      'Request sunset clause or review period',
      'Negotiate alternative compensation',
    ];
  }

  private estimateDuration(points: NegotiationPoint[]): number {
    // Estimate 3-5 minutes per negotiation point
    return Math.max(15, points.length * 4);
  }
}
