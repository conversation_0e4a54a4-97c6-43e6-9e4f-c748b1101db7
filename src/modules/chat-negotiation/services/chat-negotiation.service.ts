import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ChatNegotiationSession, ChatNegotiationSessionDocument } from '../schemas/chat-negotiation-session.schema';
import { ChatMoveData, ChatMoveDataDocument } from '../schemas/chat-move-data.schema';
import { NegotiationSimulatorService } from '../../documents/services/negotiation-simulator.service';
import { GamificationService } from '../../gamification/services/gamification.service';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { DocumentNegotiationService } from './document-negotiation.service';

@Injectable()
export class ChatNegotiationService {
  private readonly logger = new Logger(ChatNegotiationService.name);

  constructor(
    @InjectModel(ChatNegotiationSession.name)
    private chatNegotiationModel: Model<ChatNegotiationSessionDocument>,
    @InjectModel(ChatMoveData.name)
    private chatMoveDataModel: Model<ChatMoveDataDocument>,
    private negotiationSimulatorService: NegotiationSimulatorService,
    private gamificationService: GamificationService,
    private tenantContextService: TenantContextService,
    private documentNegotiationService: DocumentNegotiationService,
  ) {}

  async createSession(userId: string, scenarioId: string, aiPersonality?: any, metadata?: any) {
    const organizationId = this.tenantContextService.getCurrentOrganization();
    
    try {
      this.logger.log(`Creating chat negotiation session for user ${userId}, scenario ${scenarioId}`);

      // 1. Get scenario details to validate
      const scenarios = await this.negotiationSimulatorService.getScenarios(organizationId);
      const scenario = scenarios.find(s => s.id === scenarioId);
      if (!scenario) {
        throw new BadRequestException(`Scenario ${scenarioId} not found`);
      }

      // 2. Create negotiation session using existing service
      const negotiationSession = await this.negotiationSimulatorService.startSession(
        { scenarioId },
        userId,
        organizationId
      );

      // 3. Create chat negotiation bridge session
      const chatNegotiationSession = new this.chatNegotiationModel({
        userId,
        negotiationSessionId: (negotiationSession as any)._id,
        chatSessionId: (negotiationSession as any)._id, // For now, use same ID - will integrate with chat service later
        scenarioId,
        aiPersonality: aiPersonality || this.getDefaultPersonality(scenarioId),
        organizationId,
        metadata: metadata || {}
      });

      const savedSession = await chatNegotiationSession.save();
      
      this.logger.log(`Chat negotiation session created: ${savedSession._id}`);
      
      return {
        id: savedSession._id,
        scenarioId: savedSession.scenarioId,
        status: savedSession.status,
        currentRound: savedSession.currentRound,
        relationshipMetrics: savedSession.relationshipMetrics,
        score: savedSession.score,
        aiPersonality: savedSession.aiPersonality,
        negotiationSessionId: savedSession.negotiationSessionId,
        createdAt: (savedSession as any).createdAt
      };

    } catch (error) {
      this.logger.error(`Failed to create chat negotiation session: ${error.message}`);
      throw error;
    }
  }

  async getChatNegotiationSession(sessionId: string, userId: string) {
    const organizationId = this.tenantContextService.getCurrentOrganization();
    
    const session = await this.chatNegotiationModel.findOne({
      _id: sessionId,
      userId,
      organizationId
    }).exec();

    if (!session) {
      throw new NotFoundException('Chat negotiation session not found');
    }

    return {
      id: session._id,
      scenarioId: session.scenarioId,
      status: session.status,
      currentRound: session.currentRound,
      extractedTerms: session.extractedTerms,
      relationshipMetrics: session.relationshipMetrics,
      score: session.score,
      aiPersonality: session.aiPersonality,
      negotiationSessionId: session.negotiationSessionId,
      totalMessages: session.totalMessages,
      aiResponseTime: session.aiResponseTime,
      createdAt: (session as any).createdAt,
      updatedAt: (session as any).updatedAt,
      lastActivityAt: session.lastActivityAt
    };
  }

  async getUserSessions(userId: string, query: any = {}) {
    const organizationId = this.tenantContextService.getCurrentOrganization();
    const { status, limit = 20, offset = 0, scenarioId } = query;

    const filter: any = { userId, organizationId };
    if (status) filter.status = status;
    if (scenarioId) filter.scenarioId = scenarioId;

    const sessions = await this.chatNegotiationModel
      .find(filter)
      .sort({ lastActivityAt: -1 })
      .limit(parseInt(limit))
      .skip(parseInt(offset))
      .exec();

    const total = await this.chatNegotiationModel.countDocuments(filter);

    return {
      sessions: sessions.map(session => ({
        id: session._id,
        scenarioId: session.scenarioId,
        status: session.status,
        currentRound: session.currentRound,
        relationshipMetrics: session.relationshipMetrics,
        score: session.score,
        totalMessages: session.totalMessages,
        createdAt: (session as any).createdAt,
        lastActivityAt: session.lastActivityAt
      })),
      total,
      limit: parseInt(limit),
      offset: parseInt(offset)
    };
  }

  async processChatMove(sessionId: string, userId: string, message: string, extractedData?: any, context?: any) {
    const startTime = Date.now();
    const organizationId = this.tenantContextService.getCurrentOrganization();

    try {
      // 1. Get chat negotiation session
      const session = await this.getChatNegotiationSession(sessionId, userId);
      
      // 2. Extract/enhance data from message
      const enhancedData = await this.enhanceExtractedData(message, extractedData, session);
      
      // 3. Convert to negotiation move format
      const negotiationMove = this.convertToNegotiationMove(message, enhancedData);
      
      // 4. Process through negotiation simulator (simplified for MVP)
      const updatedNegotiation = {
        success: true,
        round: session.currentRound + 1,
        score: session.score + 0.1
      };

      // 5. Generate AI response
      const aiResponse = await this.generateAIResponse(session, enhancedData, updatedNegotiation);
      
      // 6. Update session metrics
      const relationshipUpdate = this.calculateRelationshipUpdate(
        session.relationshipMetrics,
        enhancedData,
        aiResponse
      );

      const newScore = this.calculateNewScore(session.score, enhancedData, relationshipUpdate);

      // 7. Save move data for analytics
      const moveData = new this.chatMoveDataModel({
        chatNegotiationSessionId: sessionId,
        userId,
        messageContent: message,
        sender: 'user',
        extractedData: enhancedData,
        relationshipImpact: {
          trustChange: relationshipUpdate.trust - session.relationshipMetrics.trust,
          respectChange: relationshipUpdate.respect - session.relationshipMetrics.respect,
          pressureChange: relationshipUpdate.pressure - session.relationshipMetrics.pressure
        },
        scoreImpact: newScore - session.score,
        processingTimeMs: Date.now() - startTime,
        confidence: enhancedData.confidence || 0.5,
        detectedStrategy: enhancedData.strategy,
        sentiment: enhancedData.sentiment,
        organizationId
      });

      await moveData.save();

      // 8. Save AI response data
      const aiMoveData = new this.chatMoveDataModel({
        chatNegotiationSessionId: sessionId,
        userId,
        messageContent: aiResponse.content,
        sender: 'ai',
        extractedData: aiResponse.extractedData,
        aiResponseMetadata: {
          suggestions: aiResponse.suggestions,
          responseTime: Date.now() - startTime
        },
        organizationId
      });

      await aiMoveData.save();

      // 9. Update session
      const updatedSession = await this.chatNegotiationModel.findByIdAndUpdate(
        sessionId,
        {
          currentRound: session.currentRound + 1,
          extractedTerms: { ...session.extractedTerms, ...enhancedData.offer },
          relationshipMetrics: relationshipUpdate,
          score: newScore,
          totalMessages: session.totalMessages + 2, // User + AI message
          aiResponseTime: ((session.aiResponseTime * session.totalMessages) + (Date.now() - startTime)) / (session.totalMessages + 2),
          lastActivityAt: new Date()
        },
        { new: true }
      );

      this.logger.log(`Chat move processed for session ${sessionId}, round ${updatedSession.currentRound}`);

      return {
        userMessage: {
          content: message,
          extractedData: enhancedData,
          timestamp: new Date()
        },
        aiResponse: {
          content: aiResponse.content,
          suggestions: aiResponse.suggestions,
          extractedData: aiResponse.extractedData,
          timestamp: new Date()
        },
        sessionUpdate: {
          id: updatedSession._id,
          currentRound: updatedSession.currentRound,
          relationshipMetrics: updatedSession.relationshipMetrics,
          score: updatedSession.score,
          extractedTerms: updatedSession.extractedTerms
        },
        processingTime: Date.now() - startTime
      };

    } catch (error) {
      this.logger.error(`Failed to process chat move: ${error.message}`);
      throw error;
    }
  }

  private getDefaultPersonality(scenarioId: string) {
    // Default AI personalities based on scenario
    const personalities = {
      software_licensing: {
        characterId: 'default_character',
        aggressiveness: 0.4,
        flexibility: 0.7,
        riskTolerance: 0.6,
        communicationStyle: 'ANALYTICAL'
      },
      contract_negotiation: {
        characterId: 'default_character',
        aggressiveness: 0.5,
        flexibility: 0.6,
        riskTolerance: 0.5,
        communicationStyle: 'DIPLOMATIC'
      }
    };

    return personalities[scenarioId] || personalities.software_licensing;
  }

  private async enhanceExtractedData(message: string, extractedData: any, session: any) {
    // Basic data extraction - will be enhanced in Phase 2
    const enhanced = {
      ...extractedData,
      message,
      timestamp: new Date(),
      confidence: extractedData?.confidence || 0.5
    };

    // Extract basic financial terms if not provided
    if (!enhanced.offer?.price) {
      const priceMatch = message.match(/\$?([\d,]+(?:\.\d{2})?)\s*(?:k|thousand)?/i);
      if (priceMatch) {
        enhanced.offer = enhanced.offer || {};
        enhanced.offer.price = parseFloat(priceMatch[1].replace(/,/g, ''));
        if (message.toLowerCase().includes('k') || message.toLowerCase().includes('thousand')) {
          enhanced.offer.price *= 1000;
        }
        enhanced.offer.currency = 'USD';
      }
    }

    // Detect basic strategy if not provided
    if (!enhanced.strategy) {
      enhanced.strategy = this.detectBasicStrategy(message);
    }

    // Detect basic sentiment if not provided
    if (!enhanced.sentiment) {
      enhanced.sentiment = this.detectBasicSentiment(message);
    }

    return enhanced;
  }

  private convertToNegotiationMove(message: string, extractedData: any) {
    return {
      type: 'offer',
      content: {
        message,
        offer: extractedData.offer || {},
        terms: extractedData.terms || []
      },
      strategy: extractedData.strategy || 'collaborative',
      metadata: {
        source: 'chat',
        extractedData,
        confidence: extractedData.confidence || 0.5
      }
    };
  }

  private async generateAIResponse(session: any, userMoveData: any, negotiationContext: any) {
    // Check if this is a document-based negotiation
    const documentContext = await this.getDocumentContextForResponse(session.id);

    let baseResponse: string;
    let suggestions: string[];

    if (documentContext) {
      // Generate document-aware response
      baseResponse = await this.generateDocumentAwareResponse(session, userMoveData, documentContext);
      suggestions = this.generateDocumentAwareSuggestions(documentContext, userMoveData);
    } else {
      // Use basic template-based response
      const responses = this.getResponseTemplates(session.scenarioId);
      baseResponse = this.selectResponse(responses, userMoveData, session.aiPersonality);
      suggestions = [
        "What's most important to you in this deal?",
        "Are there other terms we should discuss?",
        "Can we find a middle ground here?"
      ];
    }

    return {
      content: baseResponse,
      suggestions,
      extractedData: {
        strategy: 'collaborative',
        sentiment: 'positive'
      },
      relationshipImpact: {
        trust: 0.1,
        respect: 0.05,
        pressure: -0.05
      }
    };
  }

  private async getDocumentContextForResponse(sessionId: string): Promise<string | null> {
    try {
      return await this.documentNegotiationService.getNegotiationContext(sessionId);
    } catch (error) {
      this.logger.warn(`Could not get document context for session ${sessionId}: ${error.message}`);
      return null;
    }
  }

  private async generateDocumentAwareResponse(session: any, userMoveData: any, documentContext: string): Promise<string> {
    // Extract key contract issues from context
    const contextLines = documentContext.split('\n').filter(line => line.trim());
    const contractType = contextLines.find(line => line.includes('Contract Type:'))?.split(':')[1]?.trim() || 'contract';
    const riskLevel = contextLines.find(line => line.includes('Risk Level:'))?.split(':')[1]?.trim() || 'MEDIUM';

    // Generate contextual response based on user's message and contract issues
    if (userMoveData.offer?.price) {
      return this.generatePriceResponseWithContext(userMoveData.offer.price, contractType, riskLevel);
    } else if (userMoveData.message?.toLowerCase().includes('term')) {
      return this.generateTermsResponseWithContext(contractType, riskLevel);
    } else {
      return this.generateGeneralResponseWithContext(contractType, riskLevel);
    }
  }

  private generatePriceResponseWithContext(price: number, contractType: string, riskLevel: string): string {
    const responses = [
      `That's an interesting price point for a ${contractType}. Given the risk profile we've identified, let's discuss how this aligns with the contract terms.`,
      `I see you're proposing $${price.toLocaleString()}. Considering the issues we found in the contract analysis, we should also address the liability and risk allocation.`,
      `Your pricing is noted. However, based on our contract review, there are several terms that could impact the overall value proposition. Should we discuss those?`
    ];

    return responses[Math.floor(Math.random() * responses.length)];
  }

  private generateTermsResponseWithContext(contractType: string, riskLevel: string): string {
    const responses = [
      `Good point about the terms. Our analysis of this ${contractType} identified several areas where we could improve the language to better protect both parties.`,
      `Terms are definitely important here. The contract review highlighted some ${riskLevel.toLowerCase()} risk areas that we should address in our negotiation.`,
      `I agree that terms matter. Based on the contract analysis, there are specific clauses we should focus on to reduce risk and improve clarity.`
    ];

    return responses[Math.floor(Math.random() * responses.length)];
  }

  private generateGeneralResponseWithContext(contractType: string, riskLevel: string): string {
    const responses = [
      `I appreciate your perspective. Since we're working with a ${contractType} that has ${riskLevel.toLowerCase()} risk elements, let's make sure we address the key issues identified in our analysis.`,
      `That's a valid point. Our contract review process identified several areas for improvement. How would you like to prioritize addressing these issues?`,
      `I understand your position. Given what we found in the contract analysis, there are some important terms we should negotiate to ensure this works for both parties.`
    ];

    return responses[Math.floor(Math.random() * responses.length)];
  }

  private generateDocumentAwareSuggestions(documentContext: string, userMoveData: any): string[] {
    const suggestions = [
      "Should we address the liability limitations identified in the contract?",
      "How do you feel about the termination clauses we found?",
      "Would you like to discuss the payment terms that were flagged?",
      "What's your priority for fixing the high-risk clauses?",
      "Can we negotiate better intellectual property protections?"
    ];

    // Return 3 random suggestions
    return suggestions.sort(() => 0.5 - Math.random()).slice(0, 3);
  }

  private getResponseTemplates(scenarioId: string) {
    return {
      price_response: [
        "That's an interesting starting point. Let me think about how we can make this work.",
        "I appreciate your offer. Can we explore the value proposition a bit more?",
        "That price point is worth considering. What flexibility do we have on terms?"
      ],
      general_response: [
        "I understand your perspective. Let's see how we can align our interests.",
        "That's a good point. How do you see this benefiting both parties?",
        "I appreciate you sharing that. What would make this ideal for you?"
      ]
    };
  }

  private selectResponse(templates: any, userMoveData: any, aiPersonality: any) {
    const responseType = userMoveData.offer?.price ? 'price_response' : 'general_response';
    const responses = templates[responseType];
    return responses[Math.floor(Math.random() * responses.length)];
  }

  private detectBasicStrategy(message: string) {
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('win-win') || lowerMessage.includes('together') || lowerMessage.includes('mutual')) {
      return 'collaborative';
    }
    if (lowerMessage.includes('need') || lowerMessage.includes('must') || lowerMessage.includes('final')) {
      return 'competitive';
    }
    if (lowerMessage.includes('flexible') || lowerMessage.includes('open') || lowerMessage.includes('willing')) {
      return 'accommodating';
    }
    if (lowerMessage.includes('data') || lowerMessage.includes('market') || lowerMessage.includes('research')) {
      return 'analytical';
    }
    
    return 'collaborative'; // Default
  }

  private detectBasicSentiment(message: string) {
    const lowerMessage = message.toLowerCase();
    
    const positiveWords = ['great', 'excellent', 'perfect', 'love', 'excited', 'happy'];
    const negativeWords = ['difficult', 'problem', 'issue', 'concern', 'worried', 'disappointed'];
    
    const positiveCount = positiveWords.filter(word => lowerMessage.includes(word)).length;
    const negativeCount = negativeWords.filter(word => lowerMessage.includes(word)).length;
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  private calculateRelationshipUpdate(current: any, userMove: any, aiResponse: any) {
    // Basic relationship calculation - will be enhanced in Phase 2
    const impact = aiResponse.relationshipImpact || { trust: 0, respect: 0, pressure: 0 };
    
    return {
      trust: Math.max(0, Math.min(100, current.trust + (impact.trust * 10))),
      respect: Math.max(0, Math.min(100, current.respect + (impact.respect * 10))),
      pressure: Math.max(0, Math.min(100, current.pressure + (impact.pressure * 10)))
    };
  }

  private calculateNewScore(currentScore: number, userMove: any, relationshipUpdate: any) {
    // Basic scoring - will be enhanced in Phase 2
    let scoreChange = 0;
    
    // Positive relationship changes improve score
    if (relationshipUpdate.trust > 50) scoreChange += 0.1;
    if (relationshipUpdate.respect > 50) scoreChange += 0.1;
    if (relationshipUpdate.pressure < 30) scoreChange += 0.1;
    
    // Strategy bonus
    if (userMove.strategy === 'collaborative') scoreChange += 0.05;
    
    return Math.max(0, Math.min(10, currentScore + scoreChange));
  }
}
