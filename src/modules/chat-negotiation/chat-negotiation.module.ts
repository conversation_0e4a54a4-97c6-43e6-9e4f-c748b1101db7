import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ChatNegotiationController } from './controllers/chat-negotiation.controller';
import { ChatNegotiationService } from './services/chat-negotiation.service';
import { DocumentNegotiationService } from './services/document-negotiation.service';
import { 
  ChatNegotiationSession, 
  ChatNegotiationSessionSchema 
} from './schemas/chat-negotiation-session.schema';
import {
  ChatMoveData,
  ChatMoveDataSchema
} from './schemas/chat-move-data.schema';
import { ContractAnalysis, ContractAnalysisSchema } from '../documents/schemas/contract-analysis.schema';
import { DocumentModel, DocumentSchema } from '../documents/schemas/document.schema';
import { DocumentsModule } from '../documents/documents.module';
import { GamificationModule } from '../gamification/gamification.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ChatNegotiationSession.name, schema: ChatNegotiationSessionSchema },
      { name: ChatMoveData.name, schema: ChatMoveDataSchema },
      { name: ContractAnalysis.name, schema: ContractAnalysisSchema },
      { name: DocumentModel.name, schema: DocumentSchema }
    ]),
    forwardRef(() => DocumentsModule), // For NegotiationSimulatorService
    forwardRef(() => GamificationModule), // For GamificationService
    AuthModule // For TenantContextService
  ],
  controllers: [ChatNegotiationController],
  providers: [ChatNegotiationService, DocumentNegotiationService],
  exports: [ChatNegotiationService]
})
export class ChatNegotiationModule {}
