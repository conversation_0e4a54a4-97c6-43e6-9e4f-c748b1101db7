import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { SubscriptionDocument } from '../schemas/subscription.schema';
import { SubscriptionTier } from '../enums/subscription-tier.enum';
// import { AnalyticsService } from '../../analytics/services/analytics.service';

export interface FeatureDefinition {
  name: string;
  description: string;
  category: string;
  requiredTier: SubscriptionTier;
  isNew?: boolean;
  addedDate?: Date;
  deprecatedDate?: Date;
}

export interface FeatureUpdateResult {
  totalSubscriptions: number;
  updatedSubscriptions: number;
  skippedSubscriptions: number;
  errors: Array<{
    subscriptionId: string;
    error: string;
  }>;
  newFeaturesAdded: string[];
  featuresRemoved: string[];
}

@Injectable()
export class FeatureManagementService {
  private readonly logger = new Logger(FeatureManagementService.name);

  constructor(
    @InjectModel('Subscription')
    private subscriptionModel: Model<SubscriptionDocument>,
  ) // private readonly analyticsService: AnalyticsService,
  {}

  /**
   * Get all available features organized by tier
   */
  getAllFeatures(): Record<SubscriptionTier, FeatureDefinition[]> {
    return {
      [SubscriptionTier.LAW_STUDENT]: [
        {
          name: 'basic_analysis',
          description: 'Basic document analysis',
          category: 'core',
          requiredTier: SubscriptionTier.LAW_STUDENT,
        },
        {
          name: 'document_upload',
          description: 'Document upload functionality',
          category: 'core',
          requiredTier: SubscriptionTier.LAW_STUDENT,
        },
        {
          name: 'chat',
          description: 'AI chat interface',
          category: 'core',
          requiredTier: SubscriptionTier.LAW_STUDENT,
        },
        {
          name: 'basic_comparison',
          description: 'Basic document comparison',
          category: 'core',
          requiredTier: SubscriptionTier.LAW_STUDENT,
        },
        {
          name: 'basic_citation_analysis',
          description: 'Basic citation analysis',
          category: 'core',
          requiredTier: SubscriptionTier.LAW_STUDENT,
        },
        {
          name: 'document_organization',
          description: 'Basic document organization',
          category: 'core',
          requiredTier: SubscriptionTier.LAW_STUDENT,
        },
        {
          name: 'user_feedback',
          description: 'User feedback system',
          category: 'core',
          requiredTier: SubscriptionTier.LAW_STUDENT,
        },
        // Limited trial features
        {
          name: 'precedent_analysis',
          description: 'Limited precedent analysis',
          category: 'trial',
          requiredTier: SubscriptionTier.LAW_STUDENT,
        },
        {
          name: 'deposition_preparation',
          description: 'Basic deposition features',
          category: 'trial',
          requiredTier: SubscriptionTier.LAW_STUDENT,
        },
        {
          name: 'deposition_analysis',
          description: 'Basic deposition analysis',
          category: 'trial',
          requiredTier: SubscriptionTier.LAW_STUDENT,
        },
        {
          name: 'ai_question_generation',
          description: 'Basic AI question generation',
          category: 'trial',
          requiredTier: SubscriptionTier.LAW_STUDENT,
        },
      ],
      [SubscriptionTier.LAWYER]: [
        // AI-Powered Analysis & Automation
        {
          name: 'precedent_analysis',
          description: 'Full precedent analysis',
          category: 'ai_analysis',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'clause_library',
          description: 'AI-powered clause library',
          category: 'ai_analysis',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'template_generation',
          description: 'Automated template generation',
          category: 'ai_analysis',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'document_automation',
          description: 'Document automation & generation',
          category: 'ai_analysis',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'ai_assisted_drafting',
          description: 'AI-assisted document drafting',
          category: 'ai_analysis',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'clause_intelligence',
          description: 'Smart clause suggestions',
          category: 'ai_analysis',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'related_document_generation',
          description: 'Generate related documents',
          category: 'ai_analysis',
          requiredTier: SubscriptionTier.LAWYER,
        },

        // Compliance & Risk Management
        {
          name: 'compliance_auditor',
          description: 'Automated compliance analysis',
          category: 'compliance',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'regulatory_compliance',
          description: 'Multi-framework compliance',
          category: 'compliance',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'risk_assessment',
          description: 'Document risk assessment',
          category: 'compliance',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'compliance_reporting',
          description: 'Automated compliance reporting',
          category: 'compliance',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'compliance_profiles',
          description: 'Custom compliance profiles',
          category: 'compliance',
          requiredTier: SubscriptionTier.LAWYER,
        },

        // Legal Workflow Automation
        {
          name: 'privilege_log_automation',
          description: 'Automated privilege logs',
          category: 'workflow',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'privilege_detection',
          description: 'AI privilege detection',
          category: 'workflow',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'redaction_automation',
          description: 'Automated redaction workflows',
          category: 'workflow',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'bulk_redactions',
          description: 'Bulk redaction operations',
          category: 'workflow',
          requiredTier: SubscriptionTier.LAWYER,
        },

        // Deposition & Litigation Support
        {
          name: 'deposition_preparation',
          description: 'Smart deposition preparation',
          category: 'litigation',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'deposition_analysis',
          description: 'Advanced deposition analysis',
          category: 'litigation',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'ai_question_generation',
          description: 'AI deposition questions',
          category: 'litigation',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'deposition_insights',
          description: 'Deposition performance insights',
          category: 'litigation',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'litigation_support',
          description: 'Litigation support tools',
          category: 'litigation',
          requiredTier: SubscriptionTier.LAWYER,
        },

        // Contract Management
        {
          name: 'contract_playbooks',
          description: 'AI contract playbooks',
          category: 'contracts',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'playbook_analysis',
          description: 'Contract deviation analysis',
          category: 'contracts',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'deviation_detection',
          description: 'Automated deviation detection',
          category: 'contracts',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'contract_risk_scoring',
          description: 'Contract risk assessment',
          category: 'contracts',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'negotiation_playbook',
          description: 'Negotiation strategy playbooks',
          category: 'contracts',
          requiredTier: SubscriptionTier.LAWYER,
        },

        // AI Training & Simulation
        {
          name: 'negotiation_simulator',
          description: 'Interactive negotiation training',
          category: 'training',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'negotiation_training',
          description: 'Negotiation skill development',
          category: 'training',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'scenario_management',
          description: 'Custom scenario management',
          category: 'training',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'performance_analytics',
          description: 'Training performance analytics',
          category: 'training',
          requiredTier: SubscriptionTier.LAWYER,
        },

        // Advanced Collaboration
        {
          name: 'real_time_collaboration',
          description: 'Real-time collaboration',
          category: 'collaboration',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'workflow_management',
          description: 'Advanced workflow management',
          category: 'collaboration',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'team_analytics',
          description: 'Team performance analytics',
          category: 'collaboration',
          requiredTier: SubscriptionTier.LAWYER,
        },
        {
          name: 'advanced_sharing',
          description: 'Advanced sharing options',
          category: 'collaboration',
          requiredTier: SubscriptionTier.LAWYER,
        },
      ],
      [SubscriptionTier.LAW_FIRM]: [
        // Admin-Only Features
        {
          name: 'api_access',
          description: 'Full API access',
          category: 'law_firm',
          requiredTier: SubscriptionTier.LAW_FIRM,
        },
        {
          name: 'team_collaboration',
          description: 'Advanced team collaboration',
          category: 'law_firm',
          requiredTier: SubscriptionTier.LAW_FIRM,
        },
        {
          name: 'admin_features',
          description: 'Administrative dashboard',
          category: 'law_firm',
          requiredTier: SubscriptionTier.LAW_FIRM,
        },
        {
          name: 'user_management',
          description: 'User and role management',
          category: 'law_firm',
          requiredTier: SubscriptionTier.LAW_FIRM,
        },
        {
          name: 'organization_management',
          description: 'Organization settings',
          category: 'law_firm',
          requiredTier: SubscriptionTier.LAW_FIRM,
        },
        {
          name: 'billing_management',
          description: 'Billing management',
          category: 'law_firm',
          requiredTier: SubscriptionTier.LAW_FIRM,
        },
        {
          name: 'system_analytics',
          description: 'System-wide analytics',
          category: 'law_firm',
          requiredTier: SubscriptionTier.LAW_FIRM,
        },
        {
          name: 'audit_logs',
          description: 'Comprehensive audit logging',
          category: 'law_firm',
          requiredTier: SubscriptionTier.LAW_FIRM,
        },
        {
          name: 'data_export',
          description: 'Advanced data export',
          category: 'law_firm',
          requiredTier: SubscriptionTier.LAW_FIRM,
        },
        {
          name: 'custom_integrations',
          description: 'Custom integrations',
          category: 'law_firm',
          requiredTier: SubscriptionTier.LAW_FIRM,
        },

        // Enterprise Features
        {
          name: 'predictive_analytics',
          description: 'Predictive legal analytics',
          category: 'enterprise',
          requiredTier: SubscriptionTier.LAW_FIRM,
        },
        {
          name: 'risk_forecasting',
          description: 'AI risk forecasting',
          category: 'enterprise',
          requiredTier: SubscriptionTier.LAW_FIRM,
        },
        {
          name: 'trend_analysis',
          description: 'Legal trend analysis',
          category: 'enterprise',
          requiredTier: SubscriptionTier.LAW_FIRM,
        },
        {
          name: 'custom_ai_models',
          description: 'Custom AI model training',
          category: 'enterprise',
          requiredTier: SubscriptionTier.LAW_FIRM,
        },
        {
          name: 'white_label_options',
          description: 'White-label customization',
          category: 'enterprise',
          requiredTier: SubscriptionTier.LAW_FIRM,
        },
        {
          name: 'dedicated_support',
          description: 'Dedicated support',
          category: 'enterprise',
          requiredTier: SubscriptionTier.LAW_FIRM,
        },
        {
          name: 'sla_guarantees',
          description: 'SLA guarantees',
          category: 'enterprise',
          requiredTier: SubscriptionTier.LAW_FIRM,
        },
      ],
    };
  }

  /**
   * Update all subscriptions with new features based on their tier
   */
  async updateAllSubscriptionsWithNewFeatures(
    newFeatures: FeatureDefinition[],
    dryRun: boolean = false,
  ): Promise<FeatureUpdateResult> {
    this.logger.log(
      `Starting feature update for ${newFeatures.length} new features (dryRun: ${dryRun})`,
    );

    const result: FeatureUpdateResult = {
      totalSubscriptions: 0,
      updatedSubscriptions: 0,
      skippedSubscriptions: 0,
      errors: [],
      newFeaturesAdded: newFeatures.map((f) => f.name),
      featuresRemoved: [],
    };

    try {
      // Get all active subscriptions
      const subscriptions = await this.subscriptionModel
        .find({
          status: 'active',
        })
        .exec();

      result.totalSubscriptions = subscriptions.length;

      for (const subscription of subscriptions) {
        try {
          const updated = await this.updateSubscriptionFeatures(
            subscription,
            newFeatures,
            dryRun,
          );

          if (updated) {
            result.updatedSubscriptions++;
          } else {
            result.skippedSubscriptions++;
          }
        } catch (error) {
          this.logger.error(
            `Error updating subscription ${subscription._id}: ${error.message}`,
            error.stack,
          );
          result.errors.push({
            subscriptionId: subscription._id.toString(),
            error: error.message,
          });
        }
      }

      // Track analytics
      if (!dryRun) {
        // TODO: Re-enable analytics tracking after fixing circular dependency
        // await this.analyticsService.trackFeatureUpdate({
        //   newFeaturesCount: newFeatures.length,
        //   subscriptionsUpdated: result.updatedSubscriptions,
        //   totalSubscriptions: result.totalSubscriptions,
        //   updateDate: new Date(),
        // });
        this.logger.log(
          `Analytics: ${newFeatures.length} features added to ${result.updatedSubscriptions} subscriptions`,
        );
      }

      this.logger.log(
        `Feature update completed: ${result.updatedSubscriptions}/${result.totalSubscriptions} subscriptions updated`,
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Error in bulk feature update: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Update a single subscription with new features
   */
  private async updateSubscriptionFeatures(
    subscription: SubscriptionDocument,
    newFeatures: FeatureDefinition[],
    dryRun: boolean,
  ): Promise<boolean> {
    const currentFeatures = subscription.features || [];
    const tierFeatures = this.getFeaturesForTier(subscription.tier);

    // Calculate features that should be added
    const featuresToAdd = newFeatures.filter(
      (feature) =>
        this.shouldAddFeatureToSubscription(feature, subscription.tier) &&
        !currentFeatures.includes(feature.name),
    );

    if (featuresToAdd.length === 0) {
      return false; // No updates needed
    }

    if (dryRun) {
      this.logger.log(
        `[DRY RUN] Would add ${featuresToAdd.length} features to subscription ${subscription._id}`,
      );
      return true;
    }

    // Update subscription with new features
    const updatedFeatures = [
      ...currentFeatures,
      ...featuresToAdd.map((f) => f.name),
    ];

    await this.subscriptionModel.updateOne(
      { _id: subscription._id },
      {
        $set: {
          features: updatedFeatures,
          lastFeatureUpdate: new Date(),
        },
      },
    );

    this.logger.log(
      `Updated subscription ${subscription._id} with ${featuresToAdd.length} new features`,
    );

    return true;
  }

  /**
   * Check if a feature should be added to a subscription based on tier
   */
  private shouldAddFeatureToSubscription(
    feature: FeatureDefinition,
    subscriptionTier: SubscriptionTier,
  ): boolean {
    // Feature should be added if subscription tier is equal or higher than required tier
    const tierHierarchy = {
      [SubscriptionTier.LAW_STUDENT]: 0,
      [SubscriptionTier.LAWYER]: 1,
      [SubscriptionTier.LAW_FIRM]: 2,
    };

    return (
      tierHierarchy[subscriptionTier] >= tierHierarchy[feature.requiredTier]
    );
  }

  /**
   * Get all features available for a specific tier
   */
  private getFeaturesForTier(tier: SubscriptionTier): string[] {
    const allFeatures = this.getAllFeatures();
    const features: string[] = [];

    // Add features from current tier and all lower tiers
    Object.entries(allFeatures).forEach(([tierName, tierFeatures]) => {
      if (
        this.shouldAddFeatureToSubscription(
          { requiredTier: tierName as SubscriptionTier } as FeatureDefinition,
          tier,
        )
      ) {
        features.push(...tierFeatures.map((f) => f.name));
      }
    });

    return [...new Set(features)]; // Remove duplicates
  }

  /**
   * Sync a single subscription's features with current tier definitions
   */
  async syncSubscriptionFeatures(subscriptionId: string): Promise<{
    added: string[];
    removed: string[];
    unchanged: string[];
  }> {
    const subscription = await this.subscriptionModel.findById(subscriptionId);
    if (!subscription) {
      throw new Error(`Subscription ${subscriptionId} not found`);
    }

    const currentFeatures = subscription.features || [];
    const expectedFeatures = this.getFeaturesForTier(subscription.tier);

    const added = expectedFeatures.filter((f) => !currentFeatures.includes(f));
    const removed = currentFeatures.filter(
      (f) => !expectedFeatures.includes(f),
    );
    const unchanged = currentFeatures.filter((f) =>
      expectedFeatures.includes(f),
    );

    // Update subscription
    await this.subscriptionModel.updateOne(
      { _id: subscriptionId },
      {
        $set: {
          features: expectedFeatures,
          lastFeatureUpdate: new Date(),
        },
      },
    );

    this.logger.log(
      `Synced subscription ${subscriptionId}: +${added.length} -${removed.length} =${unchanged.length}`,
    );

    return { added, removed, unchanged };
  }

  /**
   * Get feature usage statistics
   */
  async getFeatureUsageStats(): Promise<{
    totalSubscriptions: number;
    featureUsage: Record<
      string,
      {
        count: number;
        percentage: number;
        tier: SubscriptionTier;
      }
    >;
  }> {
    const subscriptions = await this.subscriptionModel
      .find({ status: 'active' })
      .exec();
    const totalSubscriptions = subscriptions.length;
    const featureUsage: Record<
      string,
      { count: number; percentage: number; tier: SubscriptionTier }
    > = {};

    // Count feature usage
    const allFeatures = this.getAllFeatures();
    Object.entries(allFeatures).forEach(([tier, features]) => {
      features.forEach((feature) => {
        const count = subscriptions.filter((sub) =>
          sub.features?.includes(feature.name),
        ).length;

        featureUsage[feature.name] = {
          count,
          percentage:
            totalSubscriptions > 0 ? (count / totalSubscriptions) * 100 : 0,
          tier: tier as SubscriptionTier,
        };
      });
    });

    return {
      totalSubscriptions,
      featureUsage,
    };
  }
}
