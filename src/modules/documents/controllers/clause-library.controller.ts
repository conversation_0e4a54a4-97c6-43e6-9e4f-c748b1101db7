import { Controller, Get, Post, Body, UseGuards, Query, Logger } from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { FeatureAvailabilityGuard } from '../../subscription/guards/feature-availability.guard';
import { RequireFeatures } from '../../subscription/decorators/require-features.decorator';
import { SkipSubscriptionCheck } from '../../subscription/decorators/skip-subscription-check.decorator';
import { ClauseLibraryService } from '../services/clause-library.service';
import { CreateClauseTemplateDto, UpdateClauseTemplateDto, IdentifyClausesDto, GenerateTemplateDto, SearchClauseTemplatesDto } from '../dto/clause-template.dto';
import { User } from '../../auth/decorators/user.decorator';
import { Organization } from '../../auth/decorators/organization.decorator';
import { UseCredits, FreeFeature } from '../../subscription/decorators/use-credits.decorator';

@Controller('documents/clause-library')
@UseGuards(JwtAuthGuard, FeatureAvailabilityGuard)
@RequireFeatures('clause_library')
@SkipSubscriptionCheck()
export class ClauseLibraryController {
  private readonly logger = new Logger(ClauseLibraryController.name);

  constructor(private readonly clauseLibraryService: ClauseLibraryService) {}

  @Post('templates')
  @FreeFeature() // Creating clause templates is free
  async createTemplate(
    @Body() createDto: CreateClauseTemplateDto,
    @Organization() organizationId: string,
    @User('sub') userId: string
  ) {
    return this.clauseLibraryService.createTemplate(createDto, organizationId, userId);
  }

  @Get('templates')
  @FreeFeature() // Viewing clause templates is free
  async findAllTemplates(
    @Organization() organizationId: string,
    @Query() searchDto?: SearchClauseTemplatesDto
  ) {
    this.logger.debug(`Finding templates for org ${organizationId}, search criteria:`, searchDto);
    return this.clauseLibraryService.findAll(organizationId, searchDto);
  }

  @Post('identify')
  @UseCredits('clause_identification') // AI clause identification consumes credits
  async identifyClauses(
    @Body() dto: IdentifyClausesDto,
    @Organization() organizationId: string,
    @User('sub') userId: string
  ) {
    return this.clauseLibraryService.identifyClauses(dto, organizationId, userId);
  }

  @Post('generate-template')
  @UseCredits('template_generation') // AI template generation consumes credits
  async generateTemplate(
    @Body() dto: GenerateTemplateDto,
    @Organization() organizationId: string,
    @User('sub') userId: string
  ) {
    return this.clauseLibraryService.generateTemplate(dto, organizationId, userId);
  }
}
