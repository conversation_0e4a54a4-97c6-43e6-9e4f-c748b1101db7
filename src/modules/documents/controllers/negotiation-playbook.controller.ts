import {
  Controller,
  Post,
  Get,
  Param,
  Body,
  UseGuards,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { SubscriptionGuard } from '../../subscription/guards/subscription.guard';
import { SubscriptionCheck } from '../../subscription/decorators/subscription-check.decorator';
import { FeatureAvailabilityGuard } from '../../subscription/guards/feature-availability.guard';
import { RequireFeatures } from '../../subscription/decorators/require-features.decorator';
import { DocumentAccessGuard } from '../guards/document-access.guard';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { NegotiationPlaybookService } from '../services/negotiation-playbook.service';
import { UseCredits } from '../../subscription/decorators/use-credits.decorator';
import { NegotiationSimulatorService } from '../services/negotiation-simulator.service';
import { DocumentProcessingService } from '../services/document-processing.service';
import { NegotiationPlaybookRequestDto } from '../dto/negotiation-playbook-request.dto';
import { CreateNegotiationScenarioDto } from '../dto/negotiation-simulator.dto';
import { NegotiationPlaybook } from '../interfaces/negotiation-playbook.interface';
import { NegotiationScenario } from '../interfaces/negotiation-simulator.interface';

@ApiTags('Negotiation Playbook')
@Controller('documents/:documentId/negotiation-playbook')
@UseGuards(JwtAuthGuard, DocumentAccessGuard, FeatureAvailabilityGuard)
@RequireFeatures('negotiation_playbook')
export class NegotiationPlaybookController {
  private readonly logger = new Logger(NegotiationPlaybookController.name);

  constructor(
    private readonly negotiationPlaybookService: NegotiationPlaybookService,
    private readonly negotiationSimulatorService: NegotiationSimulatorService,
    private readonly documentProcessingService: DocumentProcessingService,
    private readonly tenantContext: TenantContextService,
  ) {}

  /**
   * Get current user context with proper error handling
   */
  private getCurrentUserContext(): { userId: string; organizationId: string } {
    const userId = this.tenantContext.getCurrentUserId();
    const organizationId = this.tenantContext.getCurrentOrganization();

    if (!userId || !organizationId) {
      throw new UnauthorizedException('User context not found');
    }

    return { userId, organizationId };
  }

  @Post()
  @UseCredits('negotiation_playbook') // AI negotiation analysis consumes credits
  @UseGuards(SubscriptionGuard)
  @SubscriptionCheck('negotiation_playbook')
  @ApiOperation({
    summary: 'Generate a negotiation playbook for a document',
    description:
      'Analyzes a document and generates strategic negotiation recommendations',
  })
  @ApiParam({
    name: 'documentId',
    description: 'The ID of the document to analyze',
    type: String,
  })
  @ApiBody({
    description: 'Negotiation playbook generation options',
    type: NegotiationPlaybookRequestDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Negotiation playbook generated successfully',
    schema: {
      type: 'object',
      properties: {
        documentId: { type: 'string' },
        strategies: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              section: { type: 'string' },
              recommendations: { type: 'array', items: { type: 'string' } },
              riskLevel: { type: 'string', enum: ['low', 'medium', 'high'] },
              priority: { type: 'number' },
              alternativeLanguage: { type: 'string' },
              simulationScenarios: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    type: {
                      type: 'string',
                      enum: [
                        'concession',
                        'dealbreaker',
                        'leverage',
                        'compromise',
                      ],
                    },
                    trigger: { type: 'string' },
                    responseStrategy: { type: 'string' },
                    expectedOutcome: { type: 'string' },
                  },
                },
              },
            },
          },
        },
        overallAssessment: { type: 'string' },
        keyLeveragePoints: { type: 'array', items: { type: 'string' } },
        dealBreakers: { type: 'array', items: { type: 'string' } },
        timestamp: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Document not found',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters',
  })
  async generatePlaybook(
    @Param('documentId') documentId: string,
    @Body() request: NegotiationPlaybookRequestDto,
  ): Promise<NegotiationPlaybook> {
    this.logger.log(
      `Generating negotiation playbook for document ${documentId}`,
    );

    try {
      const playbook = await this.negotiationPlaybookService.generatePlaybook(
        documentId,
        {
          documentType: request.documentType,
          focusAreas: request.focusAreas,
          includeSimulations: request.includeSimulations,
          organizationPreferences: request.organizationPreferences,
        },
      );

      this.logger.log(
        `Successfully generated negotiation playbook for document ${documentId}`,
      );
      return playbook;
    } catch (error) {
      this.logger.error(
        `Failed to generate negotiation playbook for document ${documentId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get()
  @ApiOperation({
    summary: 'Get existing negotiation playbook for a document',
    description:
      'Retrieves a previously generated negotiation playbook for the specified document',
  })
  @ApiParam({
    name: 'documentId',
    description: 'The ID of the document',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Negotiation playbook retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Negotiation playbook not found for this document',
  })
  async getPlaybook(
    @Param('documentId') documentId: string,
  ): Promise<NegotiationPlaybook> {
    this.logger.log(
      `Retrieving negotiation playbook for document ${documentId}`,
    );

    const playbook =
      await this.negotiationPlaybookService.getPlaybookByDocumentId(documentId);

    if (!playbook) {
      throw new NotFoundException(
        `No negotiation playbook found for document ${documentId}`,
      );
    }

    return playbook;
  }

  @Post('create-scenario')
  @UseGuards(SubscriptionGuard)
  @SubscriptionCheck('negotiation_simulator')
  @ApiOperation({
    summary: 'Create a negotiation scenario from playbook analysis',
    description:
      'Generates a practice scenario based on the negotiation playbook analysis',
  })
  @ApiParam({
    name: 'documentId',
    description: 'The ID of the document with playbook analysis',
    type: String,
  })
  @ApiBody({
    description: 'Scenario creation options',
    schema: {
      type: 'object',
      properties: {
        difficulty: {
          type: 'string',
          enum: ['beginner', 'intermediate', 'expert'],
          description: 'Difficulty level for the scenario',
        },
        focusAreas: {
          type: 'array',
          items: { type: 'string' },
          description: 'Specific areas to focus on in the scenario',
        },
        aiPersonality: {
          type: 'object',
          properties: {
            aggressiveness: { type: 'number', minimum: 0, maximum: 1 },
            flexibility: { type: 'number', minimum: 0, maximum: 1 },
            communicationStyle: {
              type: 'string',
              enum: ['formal', 'casual', 'technical', 'diplomatic'],
            },
          },
          description: 'AI personality configuration for the scenario',
        },
        customizations: {
          type: 'object',
          properties: {
            maxRounds: { type: 'number', minimum: 1, maximum: 20 },
            timeLimit: { type: 'number', minimum: 5, maximum: 120 },
            specificTerms: {
              type: 'array',
              items: { type: 'string' },
              description: 'Specific terms to include in the negotiation',
            },
          },
          description: 'Custom scenario parameters',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Negotiation scenario created successfully from playbook',
    type: Object,
  })
  @ApiResponse({
    status: 404,
    description: 'Playbook not found for this document',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid scenario creation parameters',
  })
  async createScenarioFromPlaybook(
    @Param('documentId') documentId: string,
    @Body() request: any,
  ): Promise<NegotiationScenario> {
    const { userId, organizationId } = this.getCurrentUserContext();

    this.logger.log(
      `Creating negotiation scenario from playbook for document ${documentId}`,
    );

    try {
      // First, get the playbook for this document
      const playbook =
        await this.negotiationPlaybookService.getPlaybookByDocumentId(
          documentId,
        );

      if (!playbook) {
        throw new NotFoundException(
          `No negotiation playbook found for document ${documentId}`,
        );
      }

      // Get document information for a better scenario name
      const document = await this.documentProcessingService.getDocumentById(documentId);
      const documentName = document?.originalName || document?.filename || 'Document';

      // Extract scenario data from playbook analysis
      const scenarioData: CreateNegotiationScenarioDto = {
        name: `Practice: ${documentName} Analysis`,
        description: `Negotiation practice scenario based on document analysis and strategic recommendations`,
        industry: this.mapDocumentToIndustry(playbook),
        contractType: this.mapDocumentToContractType(playbook),
        difficulty: this.normalizeDifficulty(
          request.difficulty || this.estimateDifficultyFromPlaybook(playbook)
        ),
        parties: this.generatePartiesFromPlaybook(playbook),
        initialOffer: this.generateInitialOfferFromPlaybook(playbook),
        constraints: this.generateConstraintsFromPlaybook(
          playbook,
          request.customizations,
        ),
        timeline: {
          startDate: new Date(),
          expectedDuration: request.customizations?.timeLimit || 30,
          maxDuration: request.customizations?.maxRounds
            ? request.customizations.maxRounds * 5
            : 60,
          breakDuration: 5,
        },
        tags: ['playbook-generated', 'document-analysis'],
        isTemplate: false,
      };

      // Create the scenario using the simulator service
      const scenario = await this.negotiationSimulatorService.createScenario(
        scenarioData,
        userId,
        organizationId,
      );

      this.logger.log(
        `Successfully created negotiation scenario ${scenario.id} from playbook for document ${documentId}`,
      );

      return scenario;
    } catch (error) {
      this.logger.error(
        `Failed to create scenario from playbook for document ${documentId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Helper methods to extract scenario data from playbook
   */
  private mapDocumentToIndustry(playbook: NegotiationPlaybook): string {
    // Map based on playbook analysis or default to 'OTHER'
    const strategies = playbook.strategies || [];

    if (
      strategies.some(
        (s) =>
          s.section?.toLowerCase().includes('software') ||
          s.section?.toLowerCase().includes('technology'),
      )
    ) {
      return 'Technology';
    }
    if (
      strategies.some(
        (s) =>
          s.section?.toLowerCase().includes('health') ||
          s.section?.toLowerCase().includes('medical'),
      )
    ) {
      return 'Healthcare';
    }
    if (
      strategies.some(
        (s) =>
          s.section?.toLowerCase().includes('finance') ||
          s.section?.toLowerCase().includes('payment'),
      )
    ) {
      return 'Finance';
    }
    if (
      strategies.some(
        (s) =>
          s.section?.toLowerCase().includes('real estate') ||
          s.section?.toLowerCase().includes('property'),
      )
    ) {
      return 'Real Estate';
    }

    return 'Legal';
  }

  private mapDocumentToContractType(playbook: NegotiationPlaybook): string {
    const strategies = playbook.strategies || [];

    if (
      strategies.some(
        (s) =>
          s.section?.toLowerCase().includes('employment') ||
          s.section?.toLowerCase().includes('job'),
      )
    ) {
      return 'Employment Agreement';
    }
    if (
      strategies.some(
        (s) =>
          s.section?.toLowerCase().includes('service') ||
          s.section?.toLowerCase().includes('consulting'),
      )
    ) {
      return 'Service Agreement';
    }
    if (
      strategies.some(
        (s) =>
          s.section?.toLowerCase().includes('license') ||
          s.section?.toLowerCase().includes('software'),
      )
    ) {
      return 'Software License';
    }
    if (
      strategies.some(
        (s) =>
          s.section?.toLowerCase().includes('purchase') ||
          s.section?.toLowerCase().includes('sale'),
      )
    ) {
      return 'Purchase Agreement';
    }
    if (
      strategies.some(
        (s) =>
          s.section?.toLowerCase().includes('nda') ||
          s.section?.toLowerCase().includes('confidential'),
      )
    ) {
      return 'NDA';
    }

    return 'Other';
  }

  private estimateDifficultyFromPlaybook(
    playbook: NegotiationPlaybook,
  ): string {
    const strategies = playbook.strategies || [];
    const highRiskCount = strategies.filter(
      (s) => s.riskLevel === 'high',
    ).length;
    const totalStrategies = strategies.length;

    if (totalStrategies === 0) return 'beginner';

    const riskRatio = highRiskCount / totalStrategies;

    if (riskRatio > 0.5) return 'expert';
    if (riskRatio > 0.2) return 'intermediate';
    return 'beginner';
  }

  private normalizeDifficulty(difficulty: string): 'beginner' | 'intermediate' | 'expert' {
    const normalized = difficulty?.toLowerCase();

    if (normalized === 'beginner' || normalized === 'easy') return 'beginner';
    if (normalized === 'intermediate' || normalized === 'medium') return 'intermediate';
    if (normalized === 'expert' || normalized === 'hard' || normalized === 'advanced') return 'expert';

    // Default to beginner if invalid value
    return 'beginner';
  }

  private generatePartiesFromPlaybook(playbook: NegotiationPlaybook): any[] {
    const leveragePoints = playbook.keyLeveragePoints || [];
    const dealBreakers = playbook.dealBreakers || [];

    return [
      {
        name: 'Your Organization',
        role: 'client',
        priorities: leveragePoints.slice(0, 3),
        negotiationStyle: 'collaborative',
        constraints: {
          dealBreakers: dealBreakers.slice(0, 2),
        },
      },
      {
        name: 'Counterparty',
        role: 'vendor',
        priorities: [
          'Revenue maximization',
          'Risk mitigation',
          'Long-term relationship',
        ],
        negotiationStyle: 'competitive',
        constraints: {
          mustHaveTerms: ['Payment terms', 'Liability limitations'],
        },
      },
    ];
  }

  private generateInitialOfferFromPlaybook(playbook: NegotiationPlaybook): any {
    const strategies = playbook.strategies || [];

    return {
      price: 100000, // Default price - should be customizable
      currency: 'USD',
      paymentTerms: 'Net 30',
      warranties: ['Standard warranties'],
      liabilities: ['Limited liability'],
      customTerms: {
        keyTerms: strategies.slice(0, 3).map((s) => s.section),
      },
    };
  }

  private generateConstraintsFromPlaybook(
    playbook: NegotiationPlaybook,
    customizations?: any,
  ): any {
    const dealBreakers = playbook.dealBreakers || [];
    const leveragePoints = playbook.keyLeveragePoints || [];

    return {
      maxRounds: customizations?.maxRounds || 10,
      timeLimit: customizations?.timeLimit || 45,
      mustHaveTerms: leveragePoints.slice(0, 2),
      dealBreakers: dealBreakers.slice(0, 2),
      flexibleTerms: ['Payment schedule', 'Delivery timeline'],
    };
  }
}
