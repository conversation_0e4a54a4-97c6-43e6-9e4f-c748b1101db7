import { Injectable, CanActivate, ExecutionContext, ForbiddenException, Logger, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { DocumentsService } from '../services/documents.service';

/**
 * Guard to ensure users only have access to documents they are authorized to view
 * Based on organization membership and document sharing settings
 */
@Injectable()
export class DocumentAccessGuard implements CanActivate {
  private readonly logger = new Logger(DocumentAccessGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly tenantContext: TenantContextService,
    private readonly documentsService: DocumentsService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    
    // Skip access check for non-specific document operations
    if (this.isExemptOperation(context)) {
      this.logger.log(`Exempt operation: ${request.method} ${request.route?.path}`);
      return true;
    }
    
    // Get document ID from request params, body, or query
    const documentId = this.extractDocumentId(request);
    
    if (!documentId) {
      this.logger.log('No document ID found in request, skipping access check');
      return true; // No document ID specified, so no access check needed
    }

    const userId = this.tenantContext.getCurrentUserId();
    const organizationId = this.tenantContext.getCurrentOrganization();
    const userRole = this.tenantContext.getCurrentUserRole();
    
    if (!userId || !organizationId) {
      this.logger.warn(`Access attempt without proper tenant context: documentId=${documentId}`);
      
      // Record failed access attempt
      await this.tenantContext.recordFailedDocumentAccess(
        documentId,
        'Missing tenant context information'
      );
      
      throw new UnauthorizedException('Unauthorized document access attempt');
    }

    // Admin users in the organization have access to all documents
    if (userRole === 'law_firm') {
      this.logger.log(`Admin access granted: userId=${userId}, documentId=${documentId}`);
      
      // Record admin document access for audit trail
      await this.tenantContext.recordDocumentAccess(documentId);
      
      return true;
    }

    try {
      const document = await this.documentsService.findById(documentId);
      
      if (!document) {
        this.logger.warn(`Access attempt to non-existent document: documentId=${documentId}`);
        
        // Record failed access attempt
        await this.tenantContext.recordFailedDocumentAccess(
          documentId,
          'Document not found'
        );
        
        throw new ForbiddenException('Document not found');
      }

      // Log document access attempt for security auditing
      this.logger.log(
        `Document access attempt: userId=${userId}, documentId=${documentId}, ` +
        `userOrg=${organizationId}, metadata=${JSON.stringify(document.metadata || {})}`
      );

      // Check if document belongs to user's organization
      // Organization ID could be in top-level organizationId or in metadata.organizationId
      const docOrgId = (document as any).organizationId || document.metadata?.organizationId as string;
      
      // If document has an organization ID and it doesn't match the user's organization
      if (docOrgId && docOrgId !== organizationId) {
        this.logger.warn(
          `Cross-organization document access attempt: userId=${userId}, ` +
          `documentId=${documentId}, userOrg=${organizationId}, docOrg=${docOrgId}`
        );
        
        // Record failed access attempt with detailed reason
        await this.tenantContext.recordFailedDocumentAccess(
          documentId,
          `Cross-organization access attempt: document belongs to ${docOrgId}`
        );
        
        throw new ForbiddenException('You do not have access to this document');
      }

      // If the document doesn't have an organization ID, assign the current user's organization
      // IMPORTANT: We're only updating the document in the request, not in the database
      // This prevents overwriting the organization ID with the requesting user's organization
      if (!docOrgId && document.metadata) {
        this.logger.log(`Document ${documentId} has no organization ID - access restricted in production`);
        
        // In production, only allow access if the user is the owner
        if (process.env.NODE_ENV === 'production') {
          const ownerId = document.metadata?.ownerId as string;
          if (ownerId && ownerId !== userId) {
            this.logger.warn(
              `Unauthorized access to document without organization ID: userId=${userId}, ` +
              `documentId=${documentId}, ownerId=${ownerId}`
            );
            
            // Record failed access attempt
            await this.tenantContext.recordFailedDocumentAccess(
              documentId,
              `Document has no organization ID and user is not the owner`
            );
            
            throw new ForbiddenException('You do not have access to this document');
          }
        }
      }

      // Check document-specific access controls
      const ownerId = document.metadata?.ownerId as string;
      const sharedWith = document.metadata?.sharedWith as string[];
      
      // If document has an owner and the user is not the owner
      if (ownerId && ownerId !== userId) {
        // Check if document is shared with the user
        const hasAccess = Array.isArray(sharedWith) && sharedWith.includes(userId);
        
        if (!hasAccess) {
          this.logger.warn(
            `Unauthorized document access attempt: userId=${userId}, ` +
            `documentId=${documentId}, ownerId=${ownerId}`
          );
          
          // Record failed access attempt
          await this.tenantContext.recordFailedDocumentAccess(
            documentId,
            `User is not the owner and document is not shared with them`
          );
          
          throw new ForbiddenException('You do not have access to this document');
        }
      }

      // Store document in request for convenience in controllers
      request.document = document;
      
      // Record successful access for audit purposes
      await this.tenantContext.recordDocumentAccess(documentId);
      
      return true;
    } catch (error) {
      if (error instanceof ForbiddenException || error instanceof UnauthorizedException) {
        throw error;
      }
      
      this.logger.error(`Error checking document access: ${error.message}`, error.stack);
      
      // Record failed access attempt due to error
      await this.tenantContext.recordFailedDocumentAccess(
        documentId,
        `Error during access check: ${error.message}`
      );
      
      throw new ForbiddenException('Error checking document access');
    }
  }
  
  /**
   * Extract document ID from request (params, body, or query)
   */
  private extractDocumentId(request: any): string | undefined {
    // Check params (e.g., /documents/:id)
    if (request.params && request.params.id) {
      return request.params.id;
    }
    
    // Check body (e.g., POST with documentId in body)
    if (request.body) {
      if (request.body.documentId) {
        return request.body.documentId;
      }
      
      if (request.body.id) {
        return request.body.id;
      }
    }
    
    // Check query params (e.g., /documents?id=123)
    if (request.query) {
      if (request.query.documentId) {
        return request.query.documentId;
      }
      
      if (request.query.id) {
        return request.query.id;
      }
    }
    
    return undefined;
  }
  
  /**
   * Check if the current operation is exempt from document access check
   * (e.g., listing all documents, creating a new document)
   */
  private isExemptOperation(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const method = request.method;
    const path = request.route?.path;
    
    // For debugging
    this.logger.debug(`Checking if operation is exempt: ${method} ${path}`);
    
    // Exempt operations:
    // - GET /documents (list all documents) - but filter by organization in controller
    // - POST /documents/upload (create new document)
    if (method === 'GET' && path === '/documents') {
      return true;
    }
    
    if (method === 'POST' && (path === '/documents/upload' || path === '/documents')) {
      return true;
    }
    
    return false;
  }
}

/**
 * Decorator to require document access check
 */
export const RequireDocumentAccess = () => {
  return (target: any, key?: string, descriptor?: PropertyDescriptor) => {
    Reflect.defineMetadata('requireDocumentAccess', true, descriptor.value);
    return descriptor;
  };
};
