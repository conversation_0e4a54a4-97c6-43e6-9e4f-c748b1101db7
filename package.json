{"name": "legal-document-analyzer", "version": "0.0.1", "private": true, "scripts": {"clean": "rimraf dist node_modules package-lock.json", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "start:dev": "nest start --watch", "migrate:gamification": "ts-node scripts/migrate-gamification.ts", "seed:characters": "ts-node -e \"import('./scripts/migrate-gamification').then(m => m.migrateGamification())\"", "seed:achievements": "ts-node -e \"import('./scripts/migrate-gamification').then(m => m.migrateGamification())\"", "test:endpoints": "./scripts/test-endpoints.sh", "test:gamification": "ts-node scripts/test-gamification-endpoints.ts", "test:chat-negotiation": "./scripts/test-chat-negotiation.sh", "test": "jest", "test:e2e": "jest --config ./test/jest-e2e.json", "migrate:features": "ts-node src/scripts/migrate-subscription-features.ts", "migrate:features:dry": "ts-node src/scripts/migrate-subscription-features.ts --dry-run --sync-all", "migrate:features:sync": "ts-node src/scripts/migrate-subscription-features.ts --sync-all", "setup:stripe": "node scripts/setup-stripe-products.js", "validate:stripe": "node scripts/validate-stripe-setup.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.797.0", "@aws-sdk/s3-request-presigner": "^3.797.0", "@google/generative-ai": "^0.24.0", "@nestjs/axios": "^4.0.0", "@nestjs/bull": "^11.0.2", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^10.4.15", "@nestjs/config": "^4.0.1", "@nestjs/core": "^10.4.15", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^11.0.1", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.4.15", "@nestjs/platform-socket.io": "^10.4.18", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^8.1.1", "@nestjs/typeorm": "^10.0.0", "@nestjs/websockets": "^10.4.18", "@types/express": "^5.0.0", "@types/multer": "^1.4.12", "@types/node-cache": "^4.2.5", "@types/nodemailer": "^6.4.17", "@types/passport-google-oauth20": "^2.0.16", "@types/stripe": "^8.0.417", "axios-rate-limit": "^1.4.0", "bcrypt": "^5.1.1", "bottleneck": "^2.19.5", "bull": "^4.16.5", "cache-manager": "^6.4.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "diff-match-patch": "^1.0.5", "docx": "^9.4.1", "fast-levenshtein": "^3.0.0", "fuse.js": "^7.1.0", "mammoth": "^1.9.0", "mongodb": "^5.9.2", "mongoose": "^8.12.1", "node-cache": "^5.1.2", "nodemailer": "^7.0.2", "openai": "^4.87.3", "otplib": "^12.0.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pdf-parse": "^1.1.1", "qrcode": "^1.5.4", "redis": "^4.7.0", "reflect-metadata": "^0.2.2", "rxjs": "7.8.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sqlite3": "^5.1.7", "stripe": "^17.7.0", "typeorm": "^0.3.21", "uuid": "^11.1.0"}, "devDependencies": {"@nestjs/cli": "^10.4.9", "@nestjs/testing": "^10.4.15", "@types/bcrypt": "^5.0.2", "@types/jest": "^29.5.14", "@types/node": "20.3.1", "@types/passport": "^1.0.17", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/pdf-parse": "^1.1.5", "@types/pdfkit": "^0.13.9", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "chalk": "^5.4.1", "form-data": "^4.0.2", "jest": "^29.7.0", "pdfkit": "^0.16.0", "rimraf": "5.0.5", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.6", "ts-node": "10.9.1", "typescript": "5.1.3", "webpack": "^5.98.0"}}