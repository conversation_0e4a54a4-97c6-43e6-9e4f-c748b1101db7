# Legal Research Database Integration Plan (Phase 1: Open Databases)

## 1. Goal

To enhance the Legal Document Analyzer by automatically identifying legal citations (case law, statutes, regulations) within analyzed documents and providing direct links to their sources using free, open legal databases. This adds validation and deeper context for the user.

## 2. Scope (Phase 1)

- Integration with **CourtListener API** for case law citations.
- Integration with **GovInfo API** for U.S. federal statutes (U.S. Code) and regulations (Code of Federal Regulations - CFR).

## 3. Citation Extraction & Normalization

### 3.1 Citation Detection

- **Responsibility:** A dedicated service (`CitationExtractionService`) will be responsible for identifying potential citations within the document text or the initial AI analysis results.
- **Method:**
  - **Initial Pass (AI Prompt):** Update `src/config/system-prompt.config.ts` to explicitly instruct the AI model (OpenAI/Gemini) to identify and tag potential legal citations in a structured way within its JSON output (e.g., adding a `citations: ["citation string 1", "citation string 2"]` field or tagging within relevant clauses).
  - **Refinement (Extraction Service):** The `CitationExtractionService` will process the AI output (or potentially the raw text if the AI tagging is insufficient). It will use regular expressions and potentially more advanced parsing techniques specifically trained/designed for various legal citation formats.

### 3.2 Citation Normalization Rules

- **Case Law Citations:**

  - Remove internal spaces within reporter abbreviations (e.g., "F. 2d" → "F.2d")
  - Standardize volume-reporter separation (single space)
  - Normalize page references (remove "at", "p.", etc.)
  - Example: "Smith v. Jones, 123 F. 2d 456 (1st Cir. 1990)" → "123 F.2d 456 (1st Cir. 1990)"

- **Statute Citations:**

  - Standardize USC references (e.g., "U.S.C.", "USC", "United States Code" → "U.S.C.")
  - Normalize section symbols (§, Sec., Section → §)
  - Example: "Section 123 of Title 42, U.S. Code" → "42 U.S.C. § 123"

- **CFR Citations:**
  - Standardize "CFR" format
  - Normalize part and section references
  - Example: "40 C.F.R. Section 1500.1" → "40 CFR § 1500.1"

### 3.3 Fallback Strategies

- Implement fuzzy matching for near-matches
- Store common citation variations in a lookup table
- Use context clues (surrounding text) to disambiguate unclear citations
- Log ambiguous citations for manual review and continuous improvement

## 4. Integration Architecture & Security

### 4.1 Module Structure

- **New Module:** Introduce a `LegalResearchModule`.
- **New Services:**
  - `CitationExtractionService`: Handles identification and normalization of citations.
  - `CourtListenerService`: Encapsulates logic for querying the CourtListener API.
  - `GovInfoService`: Encapsulates logic for querying the GovInfo API.
  - `LegalResearchOrchestratorService`: Coordinates the process.

### 4.2 Security Implementation

- **API Key Management:**

  - Store API keys in environment variables (`COURT_LISTENER_API_KEY`, `GOVINFO_API_KEY`)
  - Use HashiCorp Vault or AWS Secrets Manager for production deployments
  - Implement key rotation mechanism (90-day rotation policy)

- **Configuration Security:**
  - Create separate config files for dev/staging/prod environments
  - Encrypt sensitive configuration at rest
  - Use TypeScript interfaces to ensure type safety of configuration objects

```typescript
interface ApiKeyConfig {
  key: string;
  expirationDate: Date;
  rateLimitPerHour: number;
}

interface LegalResearchConfig {
  courtListener: {
    baseUrl: string;
    apiKey: ApiKeyConfig;
    timeout: number;
  };
  govInfo: {
    baseUrl: string;
    apiKey: ApiKeyConfig;
    timeout: number;
  };
}
```

## 5. Error Handling & Reliability

### 5.1 HTTP Error Handling

- **4xx Errors:**

  - 400: Invalid request format (retry with normalized citation)
  - 401/403: API key issues (alert operations team)
  - 404: Citation not found (log for review, return graceful failure)
  - 429: Rate limit (implement exponential backoff)

- **5xx Errors:**
  - Implement circuit breaker pattern
  - Fallback to cached results if available
  - Retry with exponential backoff (max 3 attempts)

### 5.2 Retry Strategy

```typescript
interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  exponentialBase: number;
}

const defaultRetryConfig: RetryConfig = {
  maxAttempts: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  exponentialBase: 2,
};
```

### 5.3 Logging Strategy

- Log all API calls with request/response details
- Use structured logging format
- Include correlation IDs for request tracking
- Monitor error rates and patterns

## 6. Performance & Caching

### 6.1 Caching Implementation

- **Cache Levels:**

  - In-memory cache (Node-Cache) for frequent lookups
  - Redis for distributed caching
  - Filesystem cache for larger responses

- **Cache Policies:**
  - Case law: 30-day TTL
  - Statutes: 7-day TTL
  - Regulations: 24-hour TTL

```typescript
interface CacheConfig {
  ttl: number;
  checkPeriod: number;
  maxKeys: number;
}

interface CacheEntry {
  data: any;
  timestamp: Date;
  source: string;
}
```

### 6.2 Performance Optimizations

- Implement request batching for multiple citations
- Use connection pooling for database operations
- Implement rate limiting per tenant
- Use streams for large document processing

## 7. Data Structures and DTOs

### 7.1 Citation Metadata Structure

```typescript
interface CitationMetadata {
  id: string;
  rawCitation: string;
  normalizedCitation: string;
  type: 'case' | 'statute' | 'regulation';
  source: 'CourtListener' | 'GovInfo';
  metadata: {
    title?: string;
    date?: Date;
    court?: string;
    jurisdiction?: string;
    status?: 'active' | 'superseded' | 'repealed';
    version?: string;
  };
  links: {
    sourceUrl: string;
    pdfUrl?: string;
    apiUrl: string;
  };
  confidence: number;
}
```

### 7.2 Version Control

- Implement schema versioning for DTOs
- Maintain backward compatibility
- Use TypeScript decorators for validation

## 8. User Interface Guidelines

### 8.1 Citation Display

- Highlight citations using distinct styling
- Show citation status through color coding
- Implement progressive loading for metadata

### 8.2 Interactive Features

- Citation preview on hover using tooltips
- Detailed metadata in modal on click
- Quick copy functionality for citations
- Context menu for additional actions

### 8.3 Accessibility

- Ensure proper ARIA labels
- Keyboard navigation support
- Screen reader compatibility

## 9. Future-Proofing & Scalability

### 9.1 Extensibility

- Plugin architecture for new data sources
- Abstract base classes for citation handlers
- Event-driven architecture for processing

### 9.2 Integration Points

- Webhook support for updates
- REST API endpoints for citation services
- GraphQL schema support

### 9.3 Additional Sources

- Caselaw Access Project (CAP) API integration
- State-level legal database connections
- Commercial API preparation (Westlaw/LexisNexis)

### 9.4 Backward Compatibility

- Maintain version matrix for APIs
- Legacy endpoint support strategy
- Data migration tools
