# API Reference - Pro Features

## Authentication

All API endpoints require authentication using JWT tokens in the Authorization header:

```http
Authorization: Bearer YOUR_JWT_TOKEN
```

## Base URL

```
https://api.legal-analyzer.com
```

---

## Negotiation Simulator API

### Scenarios

#### Create Scenario
```http
POST /api/negotiation-simulator/scenarios
Content-Type: application/json

{
  "name": "string",
  "description": "string",
  "industry": "string",
  "contractType": "string",
  "difficulty": "beginner|intermediate|expert",
  "parties": [
    {
      "name": "string",
      "role": "buyer|seller|vendor|client|contractor|licensor|licensee",
      "priorities": ["string"],
      "negotiationStyle": "aggressive|collaborative|analytical|competitive|accommodating",
      "constraints": {},
      "budget": {
        "min": 0,
        "max": 0,
        "currency": "string"
      }
    }
  ],
  "initialOffer": {
    "price": 0,
    "currency": "string",
    "paymentTerms": "string",
    "warranties": ["string"],
    "liabilities": ["string"]
  },
  "constraints": {
    "maxRounds": 0,
    "timeLimit": 0,
    "mustHaveTerms": ["string"],
    "dealBreakers": ["string"],
    "flexibleTerms": ["string"]
  },
  "timeline": {
    "startDate": "2025-05-24T17:00:00.000Z",
    "expectedDuration": 0,
    "maxDuration": 0,
    "breakDuration": 0
  },
  "tags": ["string"]
}
```

#### List Scenarios
```http
GET /api/negotiation-simulator/scenarios?industry=Technology&difficulty=intermediate&page=1&limit=20
```

#### Get Scenario
```http
GET /api/negotiation-simulator/scenarios/{scenarioId}
```

### Sessions

#### Start Session
```http
POST /api/negotiation-simulator/sessions
Content-Type: application/json

{
  "scenarioId": "string",
  "aiPersonality": {
    "aggressiveness": 0.7,
    "flexibility": 0.5,
    "riskTolerance": 0.6,
    "communicationStyle": "formal|casual|technical|diplomatic",
    "decisionSpeed": "fast|moderate|deliberate",
    "concessionPattern": "early|gradual|late|minimal"
  },
  "customConstraints": {}
}
```

#### List Sessions
```http
GET /api/negotiation-simulator/sessions?status=active&page=1&limit=20
```

#### Get Session
```http
GET /api/negotiation-simulator/sessions/{sessionId}
```

#### Make Move
```http
POST /api/negotiation-simulator/sessions/{sessionId}/moves
Content-Type: application/json

{
  "move": {
    "offer": {
      "price": 0,
      "currency": "string",
      "paymentTerms": "string",
      "customTerms": {}
    },
    "message": "string",
    "strategy": "string",
    "reasoning": "string"
  }
}
```

#### Pause Session
```http
PUT /api/negotiation-simulator/sessions/{sessionId}/pause
```

#### Resume Session
```http
PUT /api/negotiation-simulator/sessions/{sessionId}/resume
```

#### Evaluate Session
```http
POST /api/negotiation-simulator/sessions/{sessionId}/evaluate
```

### Analytics

#### Get Analytics Overview
```http
GET /api/negotiation-simulator/analytics/overview
```

**Response:**
```json
{
  "totalSessions": 25,
  "completedSessions": 18,
  "completionRate": 0.72,
  "averageScore": 7.8,
  "averageRounds": 6.2,
  "averageDuration": 42.5,
  "recentSessions": []
}
```

---

## Compliance Auditor API

### Document Auditing

#### Audit Document
```http
POST /api/compliance/audit
Content-Type: application/json

{
  "documentId": "string",
  "frameworks": ["GDPR", "HIPAA", "SOX", "PCI-DSS"],
  "profileId": "string",
  "options": {
    "includeRecommendations": true,
    "detailedAnalysis": true,
    "riskThreshold": "low|medium|high"
  }
}
```

**Response:**
```json
{
  "auditId": "string",
  "documentId": "string",
  "status": "pending|in_progress|completed|failed",
  "overallScore": 7.5,
  "riskLevel": "low|medium|high|critical",
  "frameworks": {
    "GDPR": {
      "score": 8.2,
      "violations": 3,
      "warnings": 7,
      "compliant": 45
    }
  },
  "findings": [
    {
      "type": "violation|warning|recommendation|compliant",
      "framework": "string",
      "rule": "string",
      "description": "string",
      "location": "string",
      "severity": "low|medium|high|critical",
      "recommendation": "string"
    }
  ],
  "recommendations": []
}
```

#### Get Audit Results
```http
GET /api/compliance/audit-results?frameworks=GDPR&status=completed&page=1&limit=20&sortBy=auditDate&sortOrder=desc
```

#### Get Audit Result
```http
GET /api/compliance/audit-results/{resultId}
```

### Compliance Profiles

#### Create Profile
```http
POST /api/compliance/profiles
Content-Type: application/json

{
  "name": "string",
  "description": "string",
  "industry": "string",
  "frameworks": [
    {
      "name": "string",
      "version": "string",
      "enabled": true,
      "weight": 0.7,
      "customRules": [
        {
          "id": "string",
          "description": "string",
          "pattern": "string",
          "severity": "low|medium|high|critical",
          "category": "string"
        }
      ]
    }
  ],
  "riskThresholds": {
    "low": 0.3,
    "medium": 0.6,
    "high": 0.8
  },
  "customRequirements": []
}
```

#### List Profiles
```http
GET /api/compliance/profiles?industry=Healthcare
```

#### Get Profile
```http
GET /api/compliance/profiles/{profileId}
```

### Regulatory Frameworks

#### Get Available Frameworks
```http
GET /api/compliance/frameworks
```

**Response:**
```json
{
  "frameworks": [
    {
      "id": "gdpr",
      "name": "General Data Protection Regulation",
      "version": "2018",
      "jurisdiction": "EU",
      "categories": ["data_protection", "privacy"],
      "articles": []
    }
  ]
}
```

#### Get Framework Rules
```http
GET /api/compliance/regulations/{regulationId}/rules
```

### Analytics

#### Get Analytics Overview
```http
GET /api/compliance/analytics/overview
```

**Response:**
```json
{
  "totalAudits": 150,
  "averageScore": 7.2,
  "complianceRate": 0.78,
  "riskDistribution": {
    "low": 45,
    "medium": 78,
    "high": 27
  },
  "frameworkBreakdown": {},
  "trends": {
    "monthlyScores": [6.8, 7.1, 7.2, 7.5],
    "improvementRate": 0.15
  }
}
```

---

## Error Responses

### Standard Error Format
```json
{
  "statusCode": 400,
  "message": "Validation failed",
  "error": "Bad Request",
  "details": [
    {
      "field": "parties",
      "constraints": ["parties must be an array"]
    }
  ],
  "path": "/api/negotiation-simulator/scenarios",
  "timestamp": "2025-05-24T16:27:30.577Z"
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation error)
- `401` - Unauthorized (invalid token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `422` - Unprocessable Entity (business logic error)
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error

---

## Rate Limiting

API requests are rate limited per organization:
- **Standard Plan**: 1000 requests/hour
- **Professional Plan**: 5000 requests/hour
- **Enterprise Plan**: Unlimited

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

---

## Pagination

List endpoints support pagination:

**Query Parameters:**
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20, max: 100)
- `sortBy` - Field to sort by
- `sortOrder` - Sort direction (`asc` or `desc`)

**Response Format:**
```json
{
  "results": [],
  "total": 150,
  "page": 1,
  "limit": 20,
  "pages": 8
}
```

---

## Webhooks

Configure webhooks to receive real-time notifications:

### Negotiation Simulator Events
- `session.started` - New negotiation session started
- `session.completed` - Negotiation session completed
- `session.paused` - Session paused by user
- `move.made` - User made a negotiation move
- `ai.responded` - AI made a counter-move

### Compliance Auditor Events
- `audit.started` - Document audit initiated
- `audit.completed` - Audit completed successfully
- `audit.failed` - Audit failed due to error
- `violation.detected` - High-severity violation found
- `compliance.improved` - Compliance score improved

### Webhook Payload Example
```json
{
  "event": "audit.completed",
  "timestamp": "2025-05-24T16:30:00.000Z",
  "data": {
    "auditId": "audit-uuid",
    "documentId": "doc-uuid",
    "overallScore": 7.5,
    "riskLevel": "medium"
  }
}
```
