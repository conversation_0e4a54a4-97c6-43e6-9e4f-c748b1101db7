# Negotiation Simulator

## Overview

The Negotiation Simulator is an AI-powered training platform that allows legal professionals to practice and improve their negotiation skills through realistic, interactive scenarios. Users can engage in multi-round negotiations with AI opponents that adapt their behavior based on configurable personality profiles.

## ✨ Latest Features (v2.0)

### 🎯 Enhanced Scenario Management

- **Update Scenarios**: Modify existing scenarios with partial updates
- **Clone Scenarios**: Create copies of scenarios with custom names and descriptions
- **Template Library**: Access pre-built scenario templates for common negotiation types
- **Playbook Integration**: Generate practice scenarios directly from document analysis

### 🎮 Improved Session Control

- **Abandon Sessions**: Exit unproductive sessions with proper scoring penalties
- **Enhanced Analytics**: Track abandonment rates and session completion patterns
- **Better State Management**: Improved session status tracking and transitions

### 🔗 Seamless Integration

- **Document-to-Practice Workflow**: Convert playbook analysis into interactive practice scenarios
- **Cross-Feature Analytics**: Unified tracking across document analysis and negotiation practice
- **Template-Based Learning**: Progressive skill development from templates to custom scenarios

### 🚀 Production Ready

- **Full API Coverage**: All endpoints implemented and tested
- **Robust Error Handling**: Comprehensive validation and error responses
- **Security**: JWT authentication with tenant isolation
- **Performance**: Optimized for real-time negotiation interactions

## Features

### 🎯 **Core Capabilities**

- **Interactive Scenarios**: Create and customize negotiation scenarios for various contract types
- **AI-Powered Opponents**: Negotiate against intelligent AI with configurable personalities
- **Real-time Feedback**: Get immediate analysis and suggestions during negotiations
- **Performance Analytics**: Track improvement over time with detailed metrics
- **Multi-round Sessions**: Engage in extended negotiations with pause/resume functionality
- **Template Library**: Use pre-built scenarios or create custom templates

### 🏗️ **Architecture**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Controller    │    │    Service      │    │   AI Service    │
│                 │    │                 │    │                 │
│ • REST API      │───▶│ • Business      │───▶│ • AI Responses  │
│ • Validation    │    │   Logic         │    │ • Personality   │
│ • Auth          │    │ • Session Mgmt  │    │   Profiles      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Database      │    │   Analytics     │    │   Evaluation    │
│                 │    │                 │    │                 │
│ • Scenarios     │    │ • Performance   │    │ • Session       │
│ • Sessions      │    │   Metrics       │    │   Analysis      │
│ • User Data     │    │ • Progress      │    │ • Scoring       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## API Endpoints

### Scenario Management

#### Create Scenario

```http
POST /api/negotiation-simulator/scenarios
```

**Request Body:**

```json
{
  "name": "Software License Agreement Negotiation",
  "description": "Practice negotiating terms for a software licensing deal",
  "industry": "Technology",
  "contractType": "Software License",
  "difficulty": "intermediate",
  "parties": [
    {
      "name": "TechCorp (Buyer)",
      "role": "licensee",
      "priorities": ["Cost reduction", "Flexible terms"],
      "negotiationStyle": "collaborative",
      "constraints": {
        "maxBudget": 100000
      }
    }
  ],
  "initialOffer": {
    "price": 120000,
    "currency": "USD",
    "paymentTerms": "Annual payment"
  },
  "constraints": {
    "maxRounds": 10,
    "mustHaveTerms": ["Support included"],
    "dealBreakers": ["Unlimited liability"]
  },
  "timeline": {
    "startDate": "2025-05-24T17:00:00.000Z",
    "expectedDuration": 45,
    "maxDuration": 90
  }
}
```

#### List Scenarios

```http
GET /api/negotiation-simulator/scenarios?industry=Technology&difficulty=intermediate
```

#### Get Specific Scenario

```http
GET /api/negotiation-simulator/scenarios/{scenarioId}
```

#### Update Scenario

```http
PUT /api/negotiation-simulator/scenarios/{scenarioId}
```

**Request Body (Partial updates supported):**

```json
{
  "name": "Updated Scenario Name",
  "description": "Updated description",
  "difficulty": "expert",
  "parties": [
    {
      "name": "Updated Party",
      "role": "buyer",
      "priorities": ["Updated priorities"],
      "negotiationStyle": "aggressive"
    }
  ],
  "constraints": {
    "maxRounds": 15,
    "timeLimit": 60
  }
}
```

**Notes:**

- Only the scenario creator can update scenarios
- Partial updates are supported - only include fields you want to change
- Party IDs are automatically generated if not provided

#### Clone Scenario

```http
POST /api/negotiation-simulator/scenarios/{scenarioId}/clone
```

**Request Body (Optional):**

```json
{
  "name": "Cloned Scenario Name",
  "description": "Custom description for the clone"
}
```

**Response:**

```json
{
  "id": "new-scenario-uuid",
  "name": "Original Scenario Name (Copy)",
  "description": "Cloned from original scenario",
  "createdBy": "current-user-id",
  "isTemplate": false,
  "parties": [
    {
      "id": "new-party-uuid",
      "name": "Original Party Name",
      "role": "buyer"
    }
  ]
}
```

#### Get Template Scenarios

```http
GET /api/negotiation-simulator/scenarios/templates
```

**Response:**

```json
[
  {
    "id": "template-uuid",
    "name": "Employment Contract Template",
    "description": "Standard template for employment negotiations",
    "isTemplate": true,
    "difficulty": "intermediate",
    "industry": "Human Resources"
  }
]
```

#### Get Scenarios from Playbook

```http
GET /api/negotiation-simulator/scenarios/from-playbook/{playbookId}?page=1&limit=20
```

**Response:**

```json
{
  "scenarios": [
    {
      "id": "scenario-uuid",
      "name": "Practice: Document Analysis",
      "description": "Generated from playbook analysis",
      "tags": ["playbook-generated", "playbook-abc123"]
    }
  ],
  "total": 5,
  "page": 1,
  "limit": 20
}
```

### Session Management

#### Start Session

```http
POST /api/negotiation-simulator/sessions
```

**Request Body:**

```json
{
  "scenarioId": "scenario-uuid",
  "aiPersonality": {
    "aggressiveness": 0.7,
    "flexibility": 0.5,
    "communicationStyle": "formal"
  }
}
```

#### List User Sessions

```http
GET /api/negotiation-simulator/sessions?status=active
```

#### Get Session Details

```http
GET /api/negotiation-simulator/sessions/{sessionId}
```

### Negotiation Actions

#### Make Move

```http
POST /api/negotiation-simulator/sessions/{sessionId}/moves
```

**Request Body:**

```json
{
  "move": {
    "offer": {
      "price": 95000,
      "currency": "USD",
      "paymentTerms": "Quarterly payments"
    },
    "message": "We'd like to propose quarterly payments to improve cash flow",
    "strategy": "collaborative",
    "reasoning": "Offering flexible payment terms while maintaining price point"
  }
}
```

#### Pause Session

```http
PUT /api/negotiation-simulator/sessions/{sessionId}/pause
```

#### Resume Session

```http
PUT /api/negotiation-simulator/sessions/{sessionId}/resume
```

#### Abandon Session

```http
PUT /api/negotiation-simulator/sessions/{sessionId}/abandon
```

**Response:**

```json
{
  "message": "Session abandoned successfully"
}
```

**Notes:**

- Can only abandon active or paused sessions
- Abandoning a session applies a scoring penalty (50% reduction)
- Session status is set to 'abandoned' and cannot be resumed
- Useful for when users want to exit a session without completing it

### Negotiation Actions

#### Make Move

```http
POST /api/negotiation-simulator/sessions/{sessionId}/moves
```

**Request Body:**

```json
{
  "move": {
    "offer": {
      "price": 95000,
      "currency": "USD",
      "paymentTerms": "Quarterly payments"
    },
    "message": "We'd like to propose quarterly payments to improve cash flow",
    "strategy": "collaborative",
    "reasoning": "Offering flexible payment terms while maintaining price point"
  }
}
```

### Analytics & Evaluation

#### Evaluate Session

```http
POST /api/negotiation-simulator/sessions/{sessionId}/evaluate
```

#### Analytics Overview

```http
GET /api/negotiation-simulator/analytics/overview
```

**Response:**

```json
{
  "totalSessions": 25,
  "completedSessions": 18,
  "completionRate": 0.72,
  "averageScore": 7.8,
  "averageRounds": 6.2,
  "averageDuration": 42.5,
  "recentSessions": [...]
}
```

## Data Models

### Negotiation Scenario

```typescript
interface NegotiationScenario {
  id: string;
  name: string;
  description: string;
  industry: string;
  contractType: string;
  difficulty: 'beginner' | 'intermediate' | 'expert';
  parties: PartyProfile[];
  initialOffer: Terms;
  constraints: NegotiationConstraints;
  timeline: TimelineOptions;
  isTemplate: boolean;
  tags: string[];
  createdBy: string;
  organizationId: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### Negotiation Session

```typescript
interface NegotiationSession {
  id: string;
  scenarioId: string;
  userId: string;
  status: 'active' | 'completed' | 'paused' | 'abandoned';
  rounds: NegotiationRound[];
  metrics: NegotiationMetrics;
  aiPersonality: AIPersonalityProfile;
  startedAt: Date;
  completedAt?: Date;
  pausedAt?: Date;
}
```

### Party Profile

```typescript
interface PartyProfile {
  name: string;
  role:
    | 'buyer'
    | 'seller'
    | 'vendor'
    | 'client'
    | 'contractor'
    | 'licensor'
    | 'licensee';
  priorities: string[];
  negotiationStyle:
    | 'aggressive'
    | 'collaborative'
    | 'analytical'
    | 'competitive'
    | 'accommodating';
  constraints: Record<string, any>;
  budget?: {
    min: number;
    max: number;
    currency: string;
  };
  timeline?: {
    urgency: 'low' | 'medium' | 'high';
    deadline?: Date;
  };
}
```

### AI Personality Profile

```typescript
interface AIPersonalityProfile {
  aggressiveness: number; // 0-1
  flexibility: number; // 0-1
  riskTolerance: number; // 0-1
  communicationStyle: 'formal' | 'casual' | 'technical' | 'diplomatic';
  decisionSpeed: 'fast' | 'moderate' | 'deliberate';
  concessionPattern: 'early' | 'gradual' | 'late' | 'minimal';
}
```

### Terms Structure

```typescript
interface Terms {
  price?: number;
  currency?: string;
  paymentTerms?: string;
  deliveryDate?: Date;
  warranties?: string[];
  liabilities?: string[];
  terminationClauses?: string[];
  intellectualProperty?: string[];
  confidentiality?: string[];
  customTerms?: Record<string, any>;
}
```

## Playbook Integration

### Create Scenario from Playbook

```http
POST /api/documents/{documentId}/negotiation-playbook/create-scenario
```

**Request Body:**

```json
{
  "difficulty": "intermediate",
  "focusAreas": ["pricing", "terms"],
  "aiPersonality": {
    "aggressiveness": 0.7,
    "flexibility": 0.5,
    "communicationStyle": "formal"
  },
  "customizations": {
    "maxRounds": 10,
    "timeLimit": 45,
    "specificTerms": ["payment terms", "liability clauses"]
  }
}
```

**Response:**

```json
{
  "id": "generated-scenario-uuid",
  "name": "Practice: Document Analysis",
  "description": "Negotiation practice scenario based on document analysis and strategic recommendations",
  "industry": "Technology",
  "contractType": "Software License",
  "difficulty": "intermediate",
  "parties": [
    {
      "name": "TechCorp (Buyer)",
      "role": "licensee",
      "priorities": ["Cost reduction", "Flexible terms", "Support coverage"],
      "negotiationStyle": "collaborative"
    },
    {
      "name": "SoftwareVendor (Seller)",
      "role": "licensor",
      "priorities": ["Revenue maximization", "Long-term relationship"],
      "negotiationStyle": "competitive"
    }
  ],
  "tags": ["playbook-generated", "document-analysis"]
}
```

**Features:**

- Automatically extracts negotiation context from playbook analysis
- Maps document type to appropriate contract type and industry
- Estimates difficulty based on risk analysis from the playbook
- Generates realistic parties based on leverage points identified
- Creates initial offers from strategic recommendations
- Sets constraints based on deal breakers and must-have terms

## Usage Examples

### 1. Complete Playbook-to-Practice Workflow

```javascript
// Step 1: Generate playbook from document
const playbook = await fetch(
  `/api/documents/${documentId}/negotiation-playbook`,
  {
    method: 'POST',
    headers: { Authorization: 'Bearer ' + token },
  },
);

// Step 2: Create practice scenario from playbook
const scenario = await fetch(
  `/api/documents/${documentId}/negotiation-playbook/create-scenario`,
  {
    method: 'POST',
    headers: {
      Authorization: 'Bearer ' + token,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      difficulty: 'intermediate',
      aiPersonality: {
        aggressiveness: 0.6,
        flexibility: 0.7,
      },
      customizations: {
        maxRounds: 8,
        timeLimit: 30,
      },
    }),
  },
);

// Step 3: Start negotiation session
const session = await fetch('/api/negotiation-simulator/sessions', {
  method: 'POST',
  headers: {
    Authorization: 'Bearer ' + token,
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    scenarioId: scenario.id,
    aiPersonality: {
      aggressiveness: 0.6,
      flexibility: 0.7,
      communicationStyle: 'diplomatic',
    },
  }),
});

// Step 4: Practice negotiation
const move = await fetch(
  `/api/negotiation-simulator/sessions/${session.id}/moves`,
  {
    method: 'POST',
    headers: {
      Authorization: 'Bearer ' + token,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      move: {
        offer: { price: 85000, currency: 'USD' },
        message: 'Based on our analysis, we propose this adjusted pricing...',
        strategy: 'value-based',
      },
    }),
  },
);
```

### 2. Creating a Basic Scenario

```javascript
const scenario = {
  name: 'Employment Contract Negotiation',
  description: 'Practice negotiating employment terms',
  industry: 'Human Resources',
  contractType: 'Employment Agreement',
  difficulty: 'beginner',
  parties: [
    {
      name: 'Job Candidate',
      role: 'contractor',
      priorities: ['Salary', 'Benefits', 'Work-life balance'],
      negotiationStyle: 'collaborative',
      constraints: { minSalary: 80000 },
    },
    {
      name: 'HR Department',
      role: 'client',
      priorities: ['Budget control', 'Talent retention'],
      negotiationStyle: 'analytical',
      constraints: { maxSalary: 120000 },
    },
  ],
  initialOffer: {
    price: 85000,
    currency: 'USD',
    paymentTerms: 'Monthly salary',
    customTerms: {
      benefits: 'Standard package',
      vacation: '15 days',
      remote: '2 days per week',
    },
  },
  constraints: {
    maxRounds: 8,
    mustHaveTerms: ['Health insurance'],
    dealBreakers: ['No vacation time'],
    flexibleTerms: ['Start date', 'Remote work'],
  },
  timeline: {
    startDate: new Date(),
    expectedDuration: 30,
    maxDuration: 60,
  },
};
```

### 2. Starting a Session with Custom AI

```javascript
const sessionConfig = {
  scenarioId: 'employment-scenario-id',
  aiPersonality: {
    aggressiveness: 0.3,
    flexibility: 0.8,
    riskTolerance: 0.6,
    communicationStyle: 'diplomatic',
    decisionSpeed: 'moderate',
    concessionPattern: 'gradual',
  },
};
```

### 3. Making a Strategic Move

```javascript
const move = {
  offer: {
    price: 95000,
    currency: 'USD',
    customTerms: {
      benefits: 'Premium package',
      vacation: '20 days',
      remote: '3 days per week',
      signingBonus: 5000,
    },
  },
  message:
    'We appreciate the opportunity and would like to propose these terms that reflect market standards for this role.',
  strategy: 'value-based',
  reasoning:
    'Emphasizing total compensation package while addressing work-life balance concerns',
};
```

## Best Practices

### Scenario Design

- **Clear Objectives**: Define specific learning goals for each scenario
- **Realistic Constraints**: Use market-appropriate terms and limitations
- **Balanced Parties**: Ensure both sides have reasonable negotiating positions
- **Progressive Difficulty**: Start with simple scenarios and increase complexity

### AI Configuration

- **Personality Matching**: Align AI behavior with the opposing party's profile
- **Adaptive Difficulty**: Adjust AI aggressiveness based on user skill level
- **Realistic Responses**: Configure AI to make believable counteroffers

### Session Management Best Practices

- **Regular Evaluation**: Use session evaluation to track learning progress
- **Strategic Pausing**: Take breaks to analyze moves and plan strategy
- **Documentation**: Record reasoning for post-session analysis
- **Abandonment Strategy**: Use abandon feature when sessions aren't productive rather than forcing completion

### Template and Cloning Strategy

- **Template Creation**: Create reusable templates for common negotiation scenarios
- **Scenario Cloning**: Clone successful scenarios to create variations for different skill levels
- **Playbook Integration**: Leverage document analysis to create realistic practice scenarios
- **Progressive Learning**: Start with templates, then move to playbook-generated scenarios

## Integration Points

### Frontend Integration

```javascript
// Enhanced React component with new features
const NegotiationSimulator = () => {
  const [session, setSession] = useState(null);
  const [scenarios, setScenarios] = useState([]);
  const [templates, setTemplates] = useState([]);
  const [currentMove, setCurrentMove] = useState('');

  // Load templates and scenarios
  useEffect(() => {
    loadTemplates();
    loadScenarios();
  }, []);

  const loadTemplates = async () => {
    const response = await fetch(
      '/api/negotiation-simulator/scenarios/templates',
      {
        headers: { Authorization: `Bearer ${token}` },
      },
    );
    const templateData = await response.json();
    setTemplates(templateData);
  };

  const loadScenarios = async () => {
    const response = await fetch('/api/negotiation-simulator/scenarios', {
      headers: { Authorization: `Bearer ${token}` },
    });
    const scenarioData = await response.json();
    setScenarios(scenarioData);
  };

  const cloneScenario = async (scenarioId, customName) => {
    const response = await fetch(
      `/api/negotiation-simulator/scenarios/${scenarioId}/clone`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          name: customName,
          description: 'Cloned for practice',
        }),
      },
    );
    const clonedScenario = await response.json();
    setScenarios([...scenarios, clonedScenario]);
    return clonedScenario;
  };

  const updateScenario = async (scenarioId, updates) => {
    const response = await fetch(
      `/api/negotiation-simulator/scenarios/${scenarioId}`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(updates),
      },
    );
    const updatedScenario = await response.json();
    setScenarios(
      scenarios.map((s) => (s.id === scenarioId ? updatedScenario : s)),
    );
    return updatedScenario;
  };

  const startSession = async (scenarioId) => {
    const response = await fetch('/api/negotiation-simulator/sessions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ scenarioId }),
    });
    const newSession = await response.json();
    setSession(newSession);
  };

  const abandonSession = async () => {
    if (!session) return;

    const response = await fetch(
      `/api/negotiation-simulator/sessions/${session.id}/abandon`,
      {
        method: 'PUT',
        headers: { Authorization: `Bearer ${token}` },
      },
    );

    if (response.ok) {
      setSession(null);
      // Show abandonment confirmation
      alert('Session abandoned. You can start a new session anytime.');
    }
  };

  const makeMove = async (move) => {
    const response = await fetch(
      `/api/negotiation-simulator/sessions/${session.id}/moves`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ move }),
      },
    );
    const result = await response.json();
    setSession(result.session);
  };

  const createFromPlaybook = async (documentId, options = {}) => {
    const response = await fetch(
      `/api/documents/${documentId}/negotiation-playbook/create-scenario`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          difficulty: options.difficulty || 'intermediate',
          aiPersonality: options.aiPersonality || {
            aggressiveness: 0.6,
            flexibility: 0.7,
          },
          customizations: options.customizations || {},
        }),
      },
    );
    const newScenario = await response.json();
    setScenarios([...scenarios, newScenario]);
    return newScenario;
  };

  return (
    <div>
      {/* Template selection */}
      <div>
        <h3>Templates</h3>
        {templates.map((template) => (
          <button
            key={template.id}
            onClick={() =>
              cloneScenario(template.id, `Practice: ${template.name}`)
            }
          >
            Use {template.name}
          </button>
        ))}
      </div>

      {/* Scenario management */}
      <div>
        <h3>My Scenarios</h3>
        {scenarios.map((scenario) => (
          <div key={scenario.id}>
            <span>{scenario.name}</span>
            <button onClick={() => startSession(scenario.id)}>Start</button>
            <button
              onClick={() =>
                cloneScenario(scenario.id, `${scenario.name} (Copy)`)
              }
            >
              Clone
            </button>
          </div>
        ))}
      </div>

      {/* Active session */}
      {session && (
        <div>
          <h3>Active Session</h3>
          <button onClick={abandonSession}>Abandon Session</button>
          {/* Negotiation interface */}
        </div>
      )}
    </div>
  );
};
```

### Analytics Dashboard

```javascript
// Analytics component
const NegotiationAnalytics = () => {
  const [analytics, setAnalytics] = useState(null);

  useEffect(() => {
    fetch('/api/negotiation-simulator/analytics/overview')
      .then((res) => res.json())
      .then(setAnalytics);
  }, []);

  return (
    <div>
      <h2>Performance Overview</h2>
      <div>Total Sessions: {analytics?.totalSessions}</div>
      <div>Average Score: {analytics?.averageScore}</div>
      <div>
        Completion Rate: {(analytics?.completionRate * 100).toFixed(1)}%
      </div>
    </div>
  );
};
```

## Error Handling

### Common Error Scenarios

- **Invalid Scenario Data**: Validation errors during scenario creation
- **Session Not Found**: Attempting to access non-existent sessions
- **Unauthorized Access**: User trying to access another user's session
- **AI Service Unavailable**: Fallback when AI service is down

### Error Response Format

```json
{
  "statusCode": 400,
  "message": "Validation failed",
  "error": "Bad Request",
  "details": [
    {
      "field": "parties",
      "constraints": ["parties must be an array"]
    }
  ],
  "path": "/api/negotiation-simulator/scenarios",
  "timestamp": "2025-05-24T16:27:30.577Z"
}
```

## Performance Considerations

### Optimization Strategies

- **Session Caching**: Cache active sessions in Redis for quick access
- **AI Response Streaming**: Stream AI responses for better user experience
- **Pagination**: Implement pagination for scenario and session lists
- **Background Processing**: Process analytics and evaluations asynchronously

### Monitoring

- **Response Times**: Track API response times for performance optimization
- **AI Service Health**: Monitor AI service availability and response quality
- **User Engagement**: Track session completion rates and user satisfaction
- **Error Rates**: Monitor and alert on high error rates

## Security

### Authentication & Authorization

- **JWT Tokens**: Secure API access with JWT authentication
- **Tenant Isolation**: Ensure users can only access their organization's data
- **Role-Based Access**: Implement different access levels for users and admins

### Data Protection

- **Encryption**: Encrypt sensitive negotiation data at rest
- **Audit Logging**: Log all user actions for compliance
- **Data Retention**: Implement data retention policies for session data
