# DocGic API Documentation

## 📋 Overview

The DocGic API provides comprehensive solutions for legal document management, contract analysis, and gamified negotiation training. Our platform combines AI-powered analysis with engaging gamification features to enhance learning and productivity.

## 🚀 Quick Start

### Prerequisites
- Valid JWT authentication token
- `contract_playbooks` feature enabled in subscription plan
- Node.js/React frontend application

### Basic Usage
```bash
# Get all playbooks
curl -X GET "http://localhost:4000/api/contract-playbooks" \
  -H "Authorization: Bearer <your-jwt-token>"

# Analyze a contract
curl -X POST "http://localhost:4000/api/contract-playbooks/analyze" \
  -H "Authorization: Bearer <your-jwt-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "contractId": "contract-uuid",
    "playbookId": "playbook-uuid",
    "options": {
      "includeRecommendations": true,
      "aiAnalysis": true
    }
  }'
```

## 📚 Documentation Files

### 1. [Gamification API](./gamification-api.md) 🎮
Complete gamification system documentation with XP, levels, achievements, characters, and leaderboards.

**Key Features:**
- User progression system (8 levels)
- AI character personalities
- Achievement system (25+ achievements)
- Real-time WebSocket updates
- Competitive leaderboards

### 2. [Contract Playbooks API](./contract-playbooks.md) 📋
Complete API documentation for automated contract analysis with customizable playbooks.

**Key Sections:**
- Authentication & Feature Gating
- Playbook Management Endpoints
- Contract Analysis Endpoints
- Utility Endpoints (Analytics, Export/Import)
- Error Handling

### 3. [TypeScript Definitions](./contract-playbooks-types.ts) 💻
Complete TypeScript type definitions for frontend integration.

**Includes:**
- Core data models (`ContractPlaybook`, `ContractAnalysis`)
- Request/Response interfaces
- Enums and utility types
- Error type definitions

### 4. [Implementation Examples](./contract-playbooks-examples.md) 🛠️
Comprehensive frontend implementation guide with React/TypeScript examples.

**Features:**
- API service layer setup
- React Query hooks
- Component examples
- Error handling patterns
- Testing strategies

## 🏗️ Architecture Overview

```
Frontend Application
├── API Client Layer
│   ├── Authentication interceptors
│   ├── Error handling
│   └── Request/Response transformation
├── Service Layer
│   ├── ContractPlaybooksService
│   ├── Type-safe API calls
│   └── Business logic
├── React Hooks Layer
│   ├── Data fetching (React Query)
│   ├── Mutations with optimistic updates
│   └── Cache management
└── UI Components
    ├── Playbook management
    ├── Contract analysis
    └── Results visualization
```

## 🔑 Key Features

### 🎮 Gamification System
- ✅ 8-level progression system (Rookie to Grandmaster)
- ✅ 25+ achievements across 4 categories
- ✅ AI character personalities with relationships
- ✅ Real-time XP and level updates
- ✅ Competitive leaderboards
- ✅ WebSocket-powered live updates

### 📋 Contract Playbooks
- ✅ Create custom contract analysis playbooks
- ✅ Define rules with severity levels and criteria
- ✅ Support for multiple contract types
- ✅ Template system for reusable playbooks
- ✅ Version control and metadata management

### 🤖 AI-Powered Analysis
- ✅ AI-powered contract analysis (10-30 second processing)
- ✅ Rule-based compliance checking
- ✅ Risk assessment and scoring (0-100 scale)
- ✅ Detailed deviation reporting
- ✅ Performance metrics and confidence scores

### 📊 Analytics & Reporting
- ✅ Playbook usage analytics
- ✅ Risk distribution analysis
- ✅ Common deviation patterns
- ✅ Time-series performance data
- ✅ Export/Import capabilities

## 🔐 Security & Access Control

### Authentication
- JWT token required for all endpoints
- Token validation on every request
- Automatic token refresh handling

### Authorization
- Organization-level data isolation
- Feature gating via subscription plans
- Role-based access control (future enhancement)

### Data Protection
- Sensitive contract data encryption
- Audit logging for compliance
- GDPR-compliant data handling

## 📊 Performance Characteristics

### Analysis Performance
- **Average Processing Time**: 10-30 seconds
- **AI Analysis Component**: 5-15 seconds
- **Rule Evaluation**: 1-5 seconds
- **Confidence Score**: Typically 85-95%

### API Performance
- **Response Times**: < 200ms for CRUD operations
- **Throughput**: 100+ concurrent analyses
- **Caching**: 5-minute cache for playbooks, 2-minute for analyses
- **Pagination**: Default 20 items, max 100 per page

## 🛠️ Development Workflow

### 1. Setup
```bash
# Install dependencies
npm install @tanstack/react-query axios react-hot-toast

# Copy type definitions
cp docs/api/contract-playbooks-types.ts src/types/
```

### 2. Implementation
```typescript
// 1. Setup API client with authentication
// 2. Create service layer with type safety
// 3. Implement React Query hooks
// 4. Build UI components
// 5. Add error handling and loading states
```

### 3. Testing
```bash
# Unit tests
npm test -- --testPathPattern=contractPlaybooks

# Integration tests
npm run test:integration

# E2E tests
npm run test:e2e
```

## 🐛 Troubleshooting

### Common Issues

#### 403 Forbidden Error
```json
{
  "statusCode": 403,
  "message": "Feature 'contract_playbooks' is not available in your current subscription plan"
}
```
**Solution**: Ensure the user's subscription includes the `contract_playbooks` feature.

#### Analysis Timeout
**Symptoms**: Analysis takes longer than 60 seconds
**Solutions**:
- Check document size (large documents take longer)
- Verify AI service availability
- Implement proper loading states
- Add retry logic for failed analyses

#### Route Conflicts
**Symptoms**: 404 errors on `/analyses` endpoints
**Solution**: Ensure specific routes are defined before parameterized routes in your routing configuration.

### Debug Mode
Enable debug logging by setting:
```typescript
// In your API client
apiClient.interceptors.request.use((config) => {
  if (process.env.NODE_ENV === 'development') {
    console.log('API Request:', config);
  }
  return config;
});
```

## 📈 Monitoring & Analytics

### Key Metrics to Track
- Analysis success/failure rates
- Average processing times
- User engagement with playbooks
- Feature adoption rates
- Error frequencies by type

### Recommended Monitoring
```typescript
// Track analysis performance
const trackAnalysis = (analysisId: string, duration: number, success: boolean) => {
  analytics.track('contract_analysis_completed', {
    analysisId,
    duration,
    success,
    timestamp: new Date().toISOString(),
  });
};
```

## 🔄 Migration Guide

### From Legacy Contract Analysis
If migrating from an existing contract analysis system:

1. **Data Migration**: Export existing rules and convert to playbook format
2. **API Updates**: Replace old endpoints with new playbook-based endpoints
3. **UI Updates**: Update components to use new data structures
4. **Testing**: Comprehensive testing of migrated functionality

### Version Compatibility
- **API Version**: v1.0.0
- **Minimum Frontend Requirements**: React 16.8+, TypeScript 4.0+
- **Recommended Libraries**: React Query v4+, Axios v1.0+

## 📞 Support

### Documentation
- [API Reference](./contract-playbooks.md) - Complete endpoint documentation
- [Type Definitions](./contract-playbooks-types.ts) - TypeScript interfaces
- [Examples](./contract-playbooks-examples.md) - Implementation examples

### Getting Help
- Check the troubleshooting section above
- Review error responses for specific guidance
- Ensure proper authentication and feature access
- Verify request/response formats match documentation

### Feature Requests
Submit feature requests with:
- Use case description
- Expected behavior
- Current workarounds
- Business impact assessment

---

**Last Updated**: January 2025  
**API Version**: 1.0.0  
**Documentation Version**: 1.0.0
