# Legal Research Assistant API Documentation

## 📋 Overview

The Legal Research Assistant API provides AI-powered legal research capabilities similar to Perplexity but specifically tailored for legal professionals. It combines real-time search across legal databases with AI synthesis to deliver comprehensive, cited legal research.

## 🔐 Authentication

All endpoints require JWT authentication and are subject to subscription tier limitations and credit usage.

```bash
Authorization: Bearer <jwt_token>
```

## 🎯 Base URL

```
/api/legal-research-assistant
```

## 📊 Feature Requirements

- **Subscription Tiers**: Available to all tiers with different limitations
- **Feature Flag**: `legal_research_assistant`
- **Credit Costs**: Varies by operation (1-3 credits per request)

---

## 🔍 Research Endpoints

### 1. Perform Legal Research Query

Executes a comprehensive legal research query across multiple sources with AI synthesis.

**Endpoint:** `POST /research/query`

**Subscription Required:** All tiers (with limitations)

**Credit Cost:** 
- Basic search: 1 credit
- With AI synthesis: 3 credits

**Request Body:**
```json
{
  "query": "What are the recent developments in data privacy law in California?",
  "options": {
    "includeSynthesis": true,
    "maxSources": 10,
    "jurisdictions": ["california", "federal"],
    "practiceAreas": ["privacy", "data_protection"],
    "timeRange": {
      "from": "2023-01-01",
      "to": "2024-12-31"
    },
    "sourceTypes": ["case_law", "statutes", "regulations", "news"],
    "synthesisStyle": "comprehensive" // "brief", "comprehensive", "analytical"
  },
  "sessionId": "optional-session-id-for-context"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "queryId": "research_query_123456",
    "sessionId": "session_789012",
    "query": "What are the recent developments in data privacy law in California?",
    "searchResults": {
      "totalSources": 8,
      "sources": [
        {
          "id": "source_1",
          "type": "case_law",
          "title": "Smith v. Facebook, Inc.",
          "citation": "2024 Cal. App. 4th 123",
          "court": "California Court of Appeal",
          "date": "2024-03-15",
          "jurisdiction": "california",
          "practiceArea": "privacy",
          "url": "https://courtlistener.com/...",
          "snippet": "The court held that...",
          "relevanceScore": 0.95,
          "authorityScore": 0.88
        },
        {
          "id": "source_2",
          "type": "statute",
          "title": "California Consumer Privacy Act Amendment",
          "citation": "Cal. Civ. Code § 1798.100",
          "effectiveDate": "2024-01-01",
          "jurisdiction": "california",
          "practiceArea": "privacy",
          "url": "https://leginfo.legislature.ca.gov/...",
          "snippet": "This amendment expands...",
          "relevanceScore": 0.92,
          "authorityScore": 0.95
        }
      ]
    },
    "aiSynthesis": {
      "summary": "Recent developments in California data privacy law show significant expansion...",
      "keyFindings": [
        "The CCPA amendments effective January 2024 expanded consumer rights",
        "Recent court decisions have clarified the scope of private right of action",
        "New regulations provide guidance on data minimization requirements"
      ],
      "legalAnalysis": "The legal landscape for data privacy in California continues to evolve...",
      "citations": [
        "Smith v. Facebook, Inc., 2024 Cal. App. 4th 123",
        "Cal. Civ. Code § 1798.100 (2024)"
      ],
      "confidenceScore": 0.89,
      "practiceImplications": [
        "Businesses should review their privacy policies by Q1 2025",
        "Consider implementing enhanced data minimization practices",
        "Monitor ongoing litigation for further clarifications"
      ]
    },
    "followUpSuggestions": [
      "How do the new CCPA amendments affect small businesses?",
      "What are the penalties for non-compliance with the updated regulations?",
      "How do California privacy laws compare to federal regulations?"
    ],
    "metadata": {
      "searchDuration": 2.3,
      "synthesisDuration": 4.7,
      "totalDuration": 7.0,
      "creditsUsed": 3,
      "timestamp": "2024-12-19T10:30:00Z"
    }
  }
}
```

### 2. Ask Follow-up Question

Continues research within an existing session context.

**Endpoint:** `POST /research/follow-up`

**Credit Cost:** 1 credit

**Request Body:**
```json
{
  "sessionId": "session_789012",
  "question": "How do the new CCPA amendments affect small businesses?",
  "options": {
    "includeSynthesis": true,
    "focusOnPrevious": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "queryId": "research_query_123457",
    "sessionId": "session_789012",
    "question": "How do the new CCPA amendments affect small businesses?",
    "contextualAnalysis": "Building on the previous research about CCPA amendments...",
    "searchResults": {
      "totalSources": 5,
      "sources": [
        // Similar structure to main query
      ]
    },
    "aiSynthesis": {
      "summary": "The CCPA amendments include specific provisions for small businesses...",
      "keyFindings": [
        "Small businesses with revenue under $25M have limited obligations",
        "New safe harbor provisions protect compliant small businesses"
      ],
      "connectionToPrevious": "This builds on the general CCPA analysis by focusing specifically on small business implications...",
      "confidenceScore": 0.87
    },
    "followUpSuggestions": [
      "What constitutes a 'small business' under the CCPA?",
      "Are there any pending bills that might change these thresholds?"
    ],
    "metadata": {
      "creditsUsed": 1,
      "timestamp": "2024-12-19T10:35:00Z"
    }
  }
}
```

---

## 📚 Session Management Endpoints

### 3. Create Research Session

Creates a new research session for tracking context.

**Endpoint:** `POST /sessions`

**Credit Cost:** 0 credits (free CRUD operation)

**Request Body:**
```json
{
  "title": "California Privacy Law Research",
  "description": "Research for client advisory on CCPA compliance",
  "tags": ["privacy", "california", "ccpa"],
  "isShared": false
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "sessionId": "session_789012",
    "title": "California Privacy Law Research",
    "description": "Research for client advisory on CCPA compliance",
    "tags": ["privacy", "california", "ccpa"],
    "createdAt": "2024-12-19T10:00:00Z",
    "isShared": false,
    "queryCount": 0
  }
}
```

### 4. Get Research Session

Retrieves a research session with its query history.

**Endpoint:** `GET /sessions/{sessionId}`

**Credit Cost:** 0 credits

**Response:**
```json
{
  "success": true,
  "data": {
    "sessionId": "session_789012",
    "title": "California Privacy Law Research",
    "description": "Research for client advisory on CCPA compliance",
    "tags": ["privacy", "california", "ccpa"],
    "createdAt": "2024-12-19T10:00:00Z",
    "updatedAt": "2024-12-19T10:35:00Z",
    "isShared": false,
    "queryCount": 2,
    "queries": [
      {
        "queryId": "research_query_123456",
        "query": "What are the recent developments in data privacy law in California?",
        "timestamp": "2024-12-19T10:30:00Z",
        "creditsUsed": 3,
        "resultSummary": "Found 8 sources with comprehensive analysis"
      },
      {
        "queryId": "research_query_123457",
        "question": "How do the new CCPA amendments affect small businesses?",
        "timestamp": "2024-12-19T10:35:00Z",
        "creditsUsed": 1,
        "resultSummary": "Found 5 sources focused on small business implications"
      }
    ],
    "totalCreditsUsed": 4
  }
}
```

### 5. List Research Sessions

Gets all research sessions for the organization.

**Endpoint:** `GET /sessions`

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)
- `tags`: Filter by tags (comma-separated)
- `search`: Search in title/description

**Credit Cost:** 0 credits

**Response:**
```json
{
  "success": true,
  "data": {
    "sessions": [
      {
        "sessionId": "session_789012",
        "title": "California Privacy Law Research",
        "description": "Research for client advisory on CCPA compliance",
        "tags": ["privacy", "california", "ccpa"],
        "createdAt": "2024-12-19T10:00:00Z",
        "updatedAt": "2024-12-19T10:35:00Z",
        "queryCount": 2,
        "totalCreditsUsed": 4,
        "isShared": false
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1,
      "totalPages": 1
    }
  }
}
```

---

## 📈 Analytics Endpoints

### 6. Get Research Analytics

Provides analytics on research usage and patterns.

**Endpoint:** `GET /analytics`

**Subscription Required:** PRO tier and above

**Query Parameters:**
- `period`: Time period ("7d", "30d", "90d", "1y")
- `groupBy`: Group results by ("day", "week", "month")

**Credit Cost:** 0 credits

**Response:**
```json
{
  "success": true,
  "data": {
    "period": "30d",
    "summary": {
      "totalQueries": 45,
      "totalSessions": 12,
      "totalCreditsUsed": 89,
      "averageQueriesPerSession": 3.75,
      "averageCreditsPerQuery": 1.98
    },
    "trends": {
      "queryVolume": [
        { "date": "2024-11-19", "queries": 3, "credits": 7 },
        { "date": "2024-11-20", "queries": 5, "credits": 12 }
      ]
    },
    "topPracticeAreas": [
      { "area": "privacy", "queries": 15, "percentage": 33.3 },
      { "area": "contracts", "queries": 12, "percentage": 26.7 }
    ],
    "topJurisdictions": [
      { "jurisdiction": "california", "queries": 18, "percentage": 40.0 },
      { "jurisdiction": "federal", "queries": 15, "percentage": 33.3 }
    ]
  }
}
```

---

## ⚠️ Error Responses

### Common Error Codes

**400 Bad Request**
```json
{
  "success": false,
  "error": {
    "code": "INVALID_QUERY",
    "message": "Query must be at least 10 characters long",
    "details": {
      "field": "query",
      "value": "short"
    }
  }
}
```

**402 Payment Required**
```json
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_CREDITS",
    "message": "Not enough credits to perform this operation",
    "details": {
      "required": 3,
      "available": 1,
      "feature": "legal_research_synthesis"
    }
  }
}
```

**403 Forbidden**
```json
{
  "success": false,
  "error": {
    "code": "FEATURE_NOT_AVAILABLE",
    "message": "Legal Research Assistant is not available in your subscription tier",
    "details": {
      "currentTier": "LAW_STUDENT",
      "requiredTier": "LAWYER",
      "feature": "legal_research_assistant"
    }
  }
}
```

**429 Too Many Requests**
```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Too many requests. Please try again later.",
    "details": {
      "retryAfter": 60,
      "limit": 10,
      "window": "1m"
    }
  }
}
```

---

## 🔧 Implementation Notes

### Subscription Tier Limitations

| Feature | Law Student | Lawyer | Law Firm |
|---------|-------------|--------|----------|
| Max Sources per Query | 5 | 15 | 25 |
| AI Synthesis | ❌ | ✅ | ✅ |
| Session History | 7 days | 90 days | Unlimited |
| Shared Sessions | ❌ | ❌ | ✅ |
| Analytics | ❌ | Basic | Advanced |

### Rate Limiting

- **Law Student**: 5 queries per hour
- **Lawyer**: 30 queries per hour  
- **Law Firm**: 100 queries per hour

### Caching Strategy

- Search results cached for 1 hour
- AI synthesis cached for 24 hours
- Session data cached for 5 minutes

---

**API Version**: 1.0  
**Last Updated**: December 2024  
**Status**: Planned Implementation
